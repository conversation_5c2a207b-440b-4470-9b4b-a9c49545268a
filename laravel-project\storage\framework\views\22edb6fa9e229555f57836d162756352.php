<!DOCTYPE html>
<html lang="vi">
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="description" content="Hệ Thống Quản Lý Hiện Đại Vớ<PERSON> G<PERSON> Diện T<PERSON>ân Thiện">
    <meta name="keywords" content="đăng nhập, hệ thống quản lý, admin template, dashboard">
    <meta name="author" content="H<PERSON> Thống Quản Lý">
    <link rel="icon" href="<?php echo e(asset('auth-assets/images/logo/favicon.png')); ?>" type="image/x-icon">
    <link rel="shortcut icon" href="<?php echo e(asset('auth-assets/images/logo/favicon.png')); ?>" type="image/x-icon">

    <title><PERSON><PERSON><PERSON> | Hệ Thống <PERSON></title>

    <!-- Font Awesome CSS -->
    <link rel="stylesheet" href="<?php echo e(asset('auth-assets/vendor/fontawesome/css/all.css')); ?>">

    <!-- Google Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Golos+Text:wght@400..900&display=swap" rel="stylesheet">

    <!-- Tabler Icons -->
    <link rel="stylesheet" type="text/css" href="<?php echo e(asset('auth-assets/vendor/tabler-icons/tabler-icons.css')); ?>">

    <!-- Bootstrap CSS -->
    <link rel="stylesheet" type="text/css" href="<?php echo e(asset('auth-assets/vendor/bootstrap/bootstrap.min.css')); ?>">

    <!-- App CSS -->
    <link rel="stylesheet" type="text/css" href="<?php echo e(asset('auth-assets/css/style.css')); ?>">

    <!-- Responsive CSS -->
    <link rel="stylesheet" type="text/css" href="<?php echo e(asset('auth-assets/css/responsive.css')); ?>">


    <style>
        /* Custom Styles For Vietnamese Text */
        h1, h2, h3, h4, h5, h6 {
            text-transform: capitalize;
        }

        .form-label {
            text-transform: capitalize;
        }

        .btn {
            text-transform: capitalize;
        }

        .form-check-label {
            text-transform: capitalize;
        }

        .link-primary {
            text-transform: capitalize;
        }

        .alert {
            text-transform: capitalize;
        }

        .app-divider-v p {
            text-transform: capitalize;
        }
    </style>
</head>
<body class="sign-in-bg">
    <div class="app-wrapper d-block">
        <div class="main-container">
            <!-- Body main section starts -->
            <div class="container">
                <div class="row sign-in-content-bg">
                    <div class="col-lg-6 image-contentbox d-none d-lg-block">
                        <div class="form-container">
                            <div class="signup-content mt-4">
                                <span>
                                    <img src="<?php echo e(asset('auth-assets/images/logo/1.png')); ?>" alt="Logo Hệ Thống" class="img-fluid">
                                </span>
                            </div>

                            <div class="signup-bg-img">
                                <img src="<?php echo e(asset('auth-assets/images/login/04.png')); ?>" alt="Đăng Nhập" class="img-fluid">
                            </div>
                        </div>
                    </div>

                    <div class="col-lg-6 form-contentbox">
                        <div class="form-container">
                            <form class="app-form" method="POST" action="<?php echo e(route('login')); ?>">
                                <?php echo csrf_field(); ?>
                                <div class="row">
                                    <div class="col-12">
                                        <div class="mb-5 text-center text-lg-start">
                                            <h2 class="text-primary f-w-600">Chào Mừng Đến Với Hệ Thống!</h2>
                                            <p>Đăng Nhập Với Thông Tin Bạn Đã Đăng Ký</p>
                                        </div>
                                    </div>

                                    <?php if(session('status')): ?>
                                        <div class="col-12">
                                            <div class="alert alert-success mb-3">
                                                <?php echo e(session('status')); ?>

                                            </div>
                                        </div>
                                    <?php endif; ?>

                                    <div class="col-12">
                                        <div class="mb-3">
                                            <label for="email" class="form-label">Địa Chỉ Email</label>
                                            <input type="email" class="form-control" placeholder="Nhập Địa Chỉ Email Của Bạn"
                                                   id="email" name="email" value="<?php echo e(old('email')); ?>" required autofocus autocomplete="username">
                                            <?php $__errorArgs = ['email'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                                <div class="text-danger mt-2"><?php echo e($message); ?></div>
                                            <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                        </div>
                                    </div>

                                    <div class="col-12">
                                        <div class="mb-3">
                                            <label for="password" class="form-label">Mật Khẩu</label>
                                            <?php if(Route::has('password.request')): ?>
                                                <a href="<?php echo e(route('password.request')); ?>" class="link-primary float-end">Quên Mật Khẩu?</a>
                                            <?php endif; ?>
                                            <input type="password" class="form-control" placeholder="Nhập Mật Khẩu Của Bạn"
                                                   id="password" name="password" required autocomplete="current-password">
                                            <?php $__errorArgs = ['password'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                                <div class="text-danger mt-2"><?php echo e($message); ?></div>
                                            <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                        </div>
                                    </div>

                                    <div class="col-12">
                                        <div class="form-check mb-3">
                                            <input class="form-check-input" type="checkbox" id="remember_me" name="remember">
                                            <label class="form-check-label text-secondary" for="remember_me">
                                                Ghi Nhớ Đăng Nhập
                                            </label>
                                        </div>
                                    </div>

                                    <div class="col-12">
                                        <div class="mb-3">
                                            <button type="submit" class="btn btn-primary w-100">Đăng Nhập</button>
                                        </div>
                                    </div>

                                    <div class="col-12">
                                        <div class="text-center text-lg-start">
                                            Chưa Có Tài Khoản? <a href="<?php echo e(route('register')); ?>"
                                                class="link-primary text-decoration-underline">Đăng Ký</a>
                                        </div>
                                    </div>

                                    <div class="app-divider-v justify-content-center">
                                        <p>Hoặc Đăng Nhập Với</p>
                                    </div>

                                    <div class="col-12">
                                        <div class="text-center">
                                            <button type="button" class="btn btn-facebook icon-btn b-r-22 m-1">
                                                <i class="ti ti-brand-facebook text-white"></i>
                                            </button>
                                            <button type="button" class="btn btn-gmail icon-btn b-r-22 m-1">
                                                <i class="ti ti-brand-google text-white"></i>
                                            </button>
                                            <button type="button" class="btn btn-github icon-btn b-r-22 m-1">
                                                <i class="ti ti-brand-github text-white"></i>
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
            <!-- Body main section ends -->
        </div>
    </div>

    <!-- Latest jQuery -->
    <script src="<?php echo e(asset('auth-assets/js/jquery-3.6.3.min.js')); ?>"></script>

    <!-- Bootstrap JS -->
    <script src="<?php echo e(asset('auth-assets/vendor/bootstrap/bootstrap.bundle.min.js')); ?>"></script>
</body>
</html>
<?php /**PATH D:\Src Công Việc\laravel-project\resources\views/auth/login.blade.php ENDPATH**/ ?>