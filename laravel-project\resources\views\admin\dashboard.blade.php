@extends('layouts.admin')

@section('title', 'Bảng Điều Khiển Quản Trị')

@section('breadcrumb')
    <li class="breadcrumb-item active">Bảng Đ<PERSON>ề<PERSON></li>
@endsection

@section('content')
<div class="row">
    <div class="col-12">
        <h1 class="mb-4">Chào Mừng Đến Với Bảng Điều Khiển Quản Trị</h1>
    </div>
</div>

<div class="row mb-4">
    <div class="col-md-3 mb-3">
        <div class="stats-card">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h6 class="mb-1">Tổng Người Dùng</h6>
                    <div class="stats-number">{{ \App\Models\User::count() }}</div>
                </div>
                <div class="stats-icon">
                    <i class="fa fa-code fa-2x opacity-75"></i>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-md-3 mb-3">
        <div class="stats-card" style="background: linear-gradient(135deg, #11998e 0%, #38ef7d 100%);">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h6 class="mb-1">Quản Trị Viên</h6>
                    <div class="stats-number">{{ \App\Models\User::where('role', 'admin')->count() }}</div>
                </div>
                <div class="stats-icon">
                    <i class="fa fa-code fa-2x opacity-75"></i>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-md-3 mb-3">
        <div class="stats-card" style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h6 class="mb-1">Người Dùng Thường</h6>
                    <div class="stats-number">{{ \App\Models\User::where('role', 'user')->count() }}</div>
                </div>
                <div class="stats-icon">
                    <i class="fa fa-code fa-2x opacity-75"></i>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-md-3 mb-3">
        <div class="stats-card" style="background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h6 class="mb-1">Hoạt Động Hôm Nay</h6>
                    <div class="stats-number">{{ \App\Models\User::whereDate('created_at', today())->count() }}</div>
                </div>
                <div class="stats-icon">
                    <i class="fa fa-code fa-2x opacity-75"></i>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <div class="col-md-8 mb-4">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">Người Dùng Mới Nhất</h5>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>Tên</th>
                                <th>Email</th>
                                <th>Vai Trò</th>
                                <th>Ngày Tạo</th>
                            </tr>
                        </thead>
                        <tbody>
                            @foreach(\App\Models\User::latest()->take(5)->get() as $user)
                            <tr>
                                <td>{{ $user->name }}</td>
                                <td>{{ $user->email }}</td>
                                <td>
                                    <span class="badge {{ $user->role === 'admin' ? 'bg-danger' : 'bg-primary' }}">
                                        {{ $user->role === 'admin' ? 'Quản Trị Viên' : 'Người Dùng' }}
                                    </span>
                                </td>
                                <td>{{ $user->created_at->format('d/m/Y H:i') }}</td>
                            </tr>
                            @endforeach
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-md-4 mb-4">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">Thông Tin Hệ Thống</h5>
            </div>
            <div class="card-body">
                <div class="mb-3">
                    <small class="text-muted">Phiên Bản Laravel</small>
                    <div class="fw-bold">{{ app()->version() }}</div>
                </div>
                
                <div class="mb-3">
                    <small class="text-muted">Phiên Bản PHP</small>
                    <div class="fw-bold">{{ PHP_VERSION }}</div>
                </div>
                
                <div class="mb-3">
                    <small class="text-muted">Múi Giờ</small>
                    <div class="fw-bold">{{ config('app.timezone') }}</div>
                </div>
                
                <div class="mb-3">
                    <small class="text-muted">Môi Trường</small>
                    <div class="fw-bold">
                        <span class="badge {{ app()->environment('production') ? 'bg-success' : 'bg-warning' }}">
                            {{ ucfirst(app()->environment()) }}
                        </span>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="card mt-3">
            <div class="card-header">
                <h5 class="card-title mb-0">Thao Tác Nhanh</h5>
            </div>
            <div class="card-body">
                <div class="d-grid gap-2">
                    <a href="{{ route('admin.users') }}" class="btn btn-primary">
                        Quản Lý Người Dùng
                    </a>
                    <button class="btn btn-outline-primary">
                        Xem Báo Cáo
                    </button>
                    <button class="btn btn-outline-secondary">
                        Cài Đặt Hệ Thống
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">Hoạt Động Gần Đây</h5>
            </div>
            <div class="card-body">
                <div class="timeline">
                    <div class="timeline-item">
                        <div class="timeline-marker bg-primary"></div>
                        <div class="timeline-content">
                            <h6 class="mb-1">Hệ Thống Khởi Động</h6>
                            <p class="text-muted mb-0">Hệ Thống Đã Được Khởi Động Thành Công</p>
                            <small class="text-muted">{{ now()->format('d/m/Y H:i') }}</small>
                        </div>
                    </div>
                    
                    <div class="timeline-item">
                        <div class="timeline-marker bg-success"></div>
                        <div class="timeline-content">
                            <h6 class="mb-1">Cơ Sở Dữ Liệu Kết Nối</h6>
                            <p class="text-muted mb-0">Kết Nối Cơ Sở Dữ Liệu Thành Công</p>
                            <small class="text-muted">{{ now()->subMinutes(5)->format('d/m/Y H:i') }}</small>
                        </div>
                    </div>
                    
                    <div class="timeline-item">
                        <div class="timeline-marker bg-info"></div>
                        <div class="timeline-content">
                            <h6 class="mb-1">Bảng Điều Khiển Sẵn Sàng</h6>
                            <p class="text-muted mb-0">Bảng Điều Khiển Quản Trị Đã Sẵn Sàng Sử Dụng</p>
                            <small class="text-muted">{{ now()->subMinutes(10)->format('d/m/Y H:i') }}</small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection

@push('styles')
<style>
    .timeline {
        position: relative;
        padding-left: 30px;
    }
    
    .timeline::before {
        content: '';
        position: absolute;
        left: 15px;
        top: 0;
        bottom: 0;
        width: 2px;
        background: #e9ecef;
    }
    
    .timeline-item {
        position: relative;
        margin-bottom: 20px;
    }
    
    .timeline-marker {
        position: absolute;
        left: -23px;
        top: 5px;
        width: 12px;
        height: 12px;
        border-radius: 50%;
        border: 2px solid white;
        box-shadow: 0 0 0 2px #e9ecef;
    }
    
    .timeline-content {
        background: #f8f9fa;
        padding: 15px;
        border-radius: 8px;
        border-left: 3px solid #667eea;
    }
</style>
@endpush
