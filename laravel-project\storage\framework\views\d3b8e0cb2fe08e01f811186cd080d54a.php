<!DOCTYPE html>
<html lang="vi">
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="description" content="Hệ Thống Quản Lý Hiện Đại Vớ<PERSON>ện T<PERSON>ân Thiện">
    <meta name="keywords" content="đăng ký, hệ thống quản lý, admin template, dashboard">
    <meta name="author" content="Hệ Thống Quản Lý">
    <title><PERSON><PERSON><PERSON> | Hệ Thống Quản Lý</title>

    <!-- Font Awesome CSS -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">

    <!-- Google Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Golos+Text:wght@400..900&display=swap" rel="stylesheet">

    <!-- Tabler Icons -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/@tabler/icons@latest/icons-sprite.svg">

    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">

    <style>
        body {
            font-family: 'Golos Text', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }

        .sign-in-bg {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .sign-in-content-bg {
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
            margin: 20px 0;
        }

        .image-contentbox {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            display: flex;
            align-items: center;
            justify-content: center;
            min-height: 700px;
        }

        .form-container {
            padding: 40px;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            height: 100%;
            text-align: center;
        }

        .signup-content img {
            max-width: 150px;
            margin-bottom: 30px;
        }

        .signup-bg-img img {
            max-width: 300px;
            opacity: 0.9;
        }

        .form-contentbox {
            padding: 40px;
            display: flex;
            align-items: center;
            min-height: 700px;
        }

        .app-form {
            width: 100%;
        }

        .text-primary {
            color: #667eea !important;
            text-transform: capitalize;
        }

        .f-w-600 {
            font-weight: 600;
            text-transform: capitalize;
        }

        .form-control {
            border-radius: 8px;
            border: 1px solid #e0e6ed;
            padding: 12px 16px;
            font-size: 14px;
            transition: all 0.3s ease;
        }

        .form-control:focus {
            border-color: #667eea;
            box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
        }

        .form-label {
            font-weight: 500;
            color: #495057;
            margin-bottom: 8px;
            text-transform: capitalize;
        }

        .btn-primary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
            border-radius: 8px;
            padding: 12px 24px;
            font-weight: 600;
            text-transform: capitalize;
        }

        .btn-primary:hover {
            transform: translateY(-1px);
            box-shadow: 0 4px 15px rgba(102, 126, 234, 0.4);
        }

        .link-primary {
            color: #667eea;
            text-decoration: none;
            text-transform: capitalize;
        }

        .link-primary:hover {
            color: #764ba2;
        }

        .form-check-label {
            text-transform: capitalize;
        }

        .app-divider-v {
            display: flex;
            align-items: center;
            margin: 20px 0;
        }

        .app-divider-v::before,
        .app-divider-v::after {
            content: '';
            flex: 1;
            height: 1px;
            background: #e0e6ed;
        }

        .app-divider-v p {
            margin: 0 15px;
            color: #6c757d;
            font-size: 14px;
            text-transform: capitalize;
        }

        .btn-facebook {
            background: #3b5998;
            border: none;
        }

        .btn-gmail {
            background: #dd4b39;
            border: none;
        }

        .btn-github {
            background: #333;
            border: none;
        }

        .icon-btn {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            display: inline-flex;
            align-items: center;
            justify-content: center;
        }

        .b-r-22 {
            border-radius: 22px;
        }

        .alert {
            text-transform: capitalize;
        }

        .logo-text {
            color: white;
            font-size: 1.5rem;
            font-weight: 700;
            text-transform: capitalize;
        }

        .welcome-subtitle {
            color: rgba(255,255,255,0.9);
            font-size: 1.1rem;
            margin-top: 20px;
            text-transform: capitalize;
        }
    </style>
</head>
<body class="sign-in-bg">
    <div class="app-wrapper d-block">
        <div class="main-container">
            <div class="container">
                <div class="row sign-in-content-bg">
                    <div class="col-lg-6 image-contentbox d-none d-lg-block">
                        <div class="form-container">
                            <div class="signup-content mt-4">
                                <div class="logo-text">
                                    <i class="fa fa-code"></i> Hệ Thống Quản Lý
                                </div>
                            </div>

                            <div class="welcome-subtitle">
                                Tham Gia Cùng Chúng Tôi!<br>
                                Tạo Tài Khoản Để Bắt Đầu Hành Trình
                            </div>

                            <div class="signup-bg-img mt-4">
                                <i class="fa fa-code" style="font-size: 120px; color: rgba(255,255,255,0.3);"></i>
                            </div>
                        </div>
                    </div>

                    <div class="col-lg-6 form-contentbox">
                        <div class="form-container">
                            <form class="app-form" method="POST" action="<?php echo e(route('register')); ?>">
                                <?php echo csrf_field(); ?>
                                <div class="row">
                                    <div class="col-12">
                                        <div class="mb-5 text-center text-lg-start">
                                            <h2 class="text-primary f-w-600">Tạo Tài Khoản</h2>
                                            <p>Bắt Đầu Miễn Phí Ngay Hôm Nay!</p>
                                        </div>
                                    </div>

                                    <div class="col-12">
                                        <div class="mb-3">
                                            <label for="name" class="form-label">Họ Và Tên</label>
                                            <input type="text" class="form-control" placeholder="Nhập Họ Và Tên Của Bạn"
                                                   id="name" name="name" value="<?php echo e(old('name')); ?>" required autofocus autocomplete="name">
                                            <?php $__errorArgs = ['name'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                                <div class="text-danger mt-2"><?php echo e($message); ?></div>
                                            <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                        </div>
                                    </div>

                                    <div class="col-12">
                                        <div class="mb-3">
                                            <label for="email" class="form-label">Địa Chỉ Email</label>
                                            <input type="email" class="form-control" placeholder="Nhập Địa Chỉ Email Của Bạn"
                                                   id="email" name="email" value="<?php echo e(old('email')); ?>" required autocomplete="username">
                                            <?php $__errorArgs = ['email'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                                <div class="text-danger mt-2"><?php echo e($message); ?></div>
                                            <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                        </div>
                                    </div>

                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="password" class="form-label">Mật Khẩu</label>
                                            <input type="password" class="form-control" placeholder="Nhập Mật Khẩu Của Bạn"
                                                   id="password" name="password" required autocomplete="new-password">
                                            <?php $__errorArgs = ['password'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                                <div class="text-danger mt-2"><?php echo e($message); ?></div>
                                            <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                        </div>
                                    </div>

                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="password_confirmation" class="form-label">Xác Nhận Mật Khẩu</label>
                                            <input type="password" class="form-control" placeholder="Nhập Lại Mật Khẩu"
                                                   id="password_confirmation" name="password_confirmation" required autocomplete="new-password">
                                            <?php $__errorArgs = ['password_confirmation'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                                <div class="text-danger mt-2"><?php echo e($message); ?></div>
                                            <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                        </div>
                                    </div>

                                    <div class="col-12">
                                        <div class="d-flex justify-content-between gap-3">
                                            <div class="form-check mb-3">
                                                <input class="form-check-input" type="checkbox" id="terms" required>
                                                <label class="form-check-label text-secondary" for="terms">
                                                    Chấp Nhận Điều Khoản & Điều Kiện
                                                </label>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="col-12">
                                        <div class="mb-3">
                                            <button type="submit" class="btn btn-primary w-100">Đăng Ký Tài Khoản</button>
                                        </div>
                                    </div>

                                    <div class="col-12">
                                        <div class="text-center text-lg-start">
                                            Đã Có Tài Khoản? <a href="<?php echo e(route('login')); ?>"
                                                class="link-primary text-decoration-underline">Đăng Nhập Ngay</a>
                                        </div>
                                    </div>

                                    <div class="app-divider-v justify-content-center">
                                        <p>Hoặc Đăng Ký Với</p>
                                    </div>

                                    <div class="col-12">
                                        <div class="text-center">
                                            <button type="button" class="btn btn-facebook icon-btn b-r-22 m-1">
                                                <i class="fab fa-facebook text-white"></i>
                                            </button>
                                            <button type="button" class="btn btn-gmail icon-btn b-r-22 m-1">
                                                <i class="fab fa-google text-white"></i>
                                            </button>
                                            <button type="button" class="btn btn-github icon-btn b-r-22 m-1">
                                                <i class="fab fa-github text-white"></i>
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- jQuery -->
    <script src="https://code.jquery.com/jquery-3.6.3.min.js"></script>
    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
<?php /**PATH D:\Src Công Việc\laravel-project\resources\views/auth/register.blade.php ENDPATH**/ ?>