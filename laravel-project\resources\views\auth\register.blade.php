<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><PERSON><PERSON><PERSON> - <PERSON></title>

    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">

    <style>
        body {
            font-family: 'Inter', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 20px 0;
        }

        .register-container {
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
            max-width: 900px;
            width: 100%;
        }

        .register-form {
            padding: 60px 40px;
        }

        .register-image {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            text-align: center;
            padding: 60px 40px;
        }

        .form-control {
            border-radius: 10px;
            border: 2px solid #e9ecef;
            padding: 12px 16px;
            font-size: 16px;
            transition: all 0.3s ease;
            text-transform: capitalize;
        }

        .form-control:focus {
            border-color: #667eea;
            box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
        }

        .btn-primary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
            border-radius: 10px;
            padding: 12px 30px;
            font-weight: 600;
            font-size: 16px;
            text-transform: capitalize;
        }

        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);
        }

        .form-label {
            font-weight: 600;
            color: #495057;
            margin-bottom: 8px;
            text-transform: capitalize;
        }

        .logo {
            font-size: 2rem;
            font-weight: 700;
            margin-bottom: 10px;
            text-transform: capitalize;
        }

        .welcome-text {
            font-size: 1.1rem;
            opacity: 0.9;
            text-transform: capitalize;
        }

        .register-title {
            font-size: 2rem;
            font-weight: 700;
            color: #333;
            margin-bottom: 10px;
            text-transform: capitalize;
        }

        .register-subtitle {
            color: #6c757d;
            margin-bottom: 30px;
            text-transform: capitalize;
        }

        .text-decoration-none {
            text-transform: capitalize;
        }

        .alert {
            text-transform: capitalize;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="register-container">
            <div class="row g-0">
                <div class="col-md-6">
                    <div class="register-image">
                        <div>
                            <div class="logo">
                                <i class="fa fa-code"></i> Hệ Thống Quản Lý
                            </div>
                            <p class="welcome-text">
                                Tham Gia Cùng Chúng Tôi!<br>
                                Tạo Tài Khoản Để Bắt Đầu Hành Trình
                            </p>
                        </div>
                    </div>
                </div>

                <div class="col-md-6">
                    <div class="register-form">
                        <h2 class="register-title">Đăng Ký</h2>
                        <p class="register-subtitle">Tạo Tài Khoản Mới Để Sử Dụng Hệ Thống</p>

                        <form method="POST" action="{{ route('register') }}">
                            @csrf

                            <div class="mb-3">
                                <label for="name" class="form-label">Họ Và Tên</label>
                                <input type="text" class="form-control" id="name" name="name"
                                       value="{{ old('name') }}" required autofocus autocomplete="name"
                                       placeholder="Nhập Họ Và Tên Của Bạn">
                                @error('name')
                                    <div class="text-danger mt-2">{{ $message }}</div>
                                @enderror
                            </div>

                            <div class="mb-3">
                                <label for="email" class="form-label">Địa Chỉ Email</label>
                                <input type="email" class="form-control" id="email" name="email"
                                       value="{{ old('email') }}" required autocomplete="username"
                                       placeholder="Nhập Địa Chỉ Email Của Bạn">
                                @error('email')
                                    <div class="text-danger mt-2">{{ $message }}</div>
                                @enderror
                            </div>

                            <div class="mb-3">
                                <label for="password" class="form-label">Mật Khẩu</label>
                                <input type="password" class="form-control" id="password" name="password"
                                       required autocomplete="new-password"
                                       placeholder="Nhập Mật Khẩu Của Bạn">
                                @error('password')
                                    <div class="text-danger mt-2">{{ $message }}</div>
                                @enderror
                            </div>

                            <div class="mb-3">
                                <label for="password_confirmation" class="form-label">Xác Nhận Mật Khẩu</label>
                                <input type="password" class="form-control" id="password_confirmation"
                                       name="password_confirmation" required autocomplete="new-password"
                                       placeholder="Nhập Lại Mật Khẩu Của Bạn">
                                @error('password_confirmation')
                                    <div class="text-danger mt-2">{{ $message }}</div>
                                @enderror
                            </div>

                            <div class="d-grid mb-3">
                                <button type="submit" class="btn btn-primary">
                                    Đăng Ký Tài Khoản
                                </button>
                            </div>

                            <div class="text-center">
                                <span class="text-muted">Đã Có Tài Khoản? </span>
                                <a href="{{ route('login') }}" class="text-decoration-none">
                                    Đăng Nhập Ngay
                                </a>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
