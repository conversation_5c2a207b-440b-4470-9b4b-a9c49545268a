@extends('layouts.user')

@section('title', 'Bảng Đ<PERSON>')

@section('content')
<!-- Hero Section -->
<div class="hero-section">
    <div class="container">
        <div class="row align-items-center">
            <div class="col-md-8">
                <h1 class="display-4 mb-3">Ch<PERSON><PERSON>, {{ Auth::user()->name }}!</h1>
                <p class="lead mb-4">
                    Chào Mừng Bạn Đến Với H<PERSON> Thống Quản Lý Cá Nhân. Khám Phá Các Tính Năng Và Dịch Vụ Của Chúng Tôi.
                </p>
                <a href="{{ route('user.profile') }}" class="btn btn-light btn-lg">
                    Xem <PERSON> Sơ Cá Nhân
                </a>
            </div>
            <div class="col-md-4 text-center">
                <div class="hero-avatar">
                    <div class="avatar-large bg-white text-primary rounded-circle d-inline-flex align-items-center justify-content-center">
                        <i class="fa fa-code fa-3x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Quick Stats -->
<div class="row mb-5">
    <div class="col-md-3 mb-4">
        <div class="card feature-card">
            <div class="feature-icon">
                <i class="fa fa-code"></i>
            </div>
            <h5>Tài Khoản Hoạt Động</h5>
            <p class="text-muted">Tài Khoản Của Bạn Đang Hoạt Động Bình Thường</p>
        </div>
    </div>
    
    <div class="col-md-3 mb-4">
        <div class="card feature-card">
            <div class="feature-icon">
                <i class="fa fa-code"></i>
            </div>
            <h5>Bảo Mật Cao</h5>
            <p class="text-muted">Thông Tin Của Bạn Được Bảo Vệ An Toàn</p>
        </div>
    </div>
    
    <div class="col-md-3 mb-4">
        <div class="card feature-card">
            <div class="feature-icon">
                <i class="fa fa-code"></i>
            </div>
            <h5>Hỗ Trợ 24/7</h5>
            <p class="text-muted">Đội Ngũ Hỗ Trợ Luôn Sẵn Sàng Giúp Đỡ</p>
        </div>
    </div>
    
    <div class="col-md-3 mb-4">
        <div class="card feature-card">
            <div class="feature-icon">
                <i class="fa fa-code"></i>
            </div>
            <h5>Cập Nhật Thường Xuyên</h5>
            <p class="text-muted">Hệ Thống Được Cập Nhật Liên Tục</p>
        </div>
    </div>
</div>

<!-- Main Content -->
<div class="row">
    <div class="col-md-8 mb-4">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">Thông Tin Tài Khoản</h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6 mb-3">
                        <label class="form-label text-muted">Tên Đầy Đủ</label>
                        <div class="fw-bold">{{ Auth::user()->name }}</div>
                    </div>
                    
                    <div class="col-md-6 mb-3">
                        <label class="form-label text-muted">Địa Chỉ Email</label>
                        <div class="fw-bold">{{ Auth::user()->email }}</div>
                    </div>
                    
                    <div class="col-md-6 mb-3">
                        <label class="form-label text-muted">Vai Trò</label>
                        <div>
                            <span class="badge bg-primary">
                                {{ Auth::user()->role === 'admin' ? 'Quản Trị Viên' : 'Người Dùng' }}
                            </span>
                        </div>
                    </div>
                    
                    <div class="col-md-6 mb-3">
                        <label class="form-label text-muted">Ngày Tham Gia</label>
                        <div class="fw-bold">{{ Auth::user()->created_at->format('d/m/Y') }}</div>
                    </div>
                </div>
                
                <hr>
                
                <div class="d-flex gap-2">
                    <a href="{{ route('user.profile') }}" class="btn btn-primary">
                        Xem Hồ Sơ Chi Tiết
                    </a>
                    <a href="{{ route('profile.edit') }}" class="btn btn-outline-primary">
                        Chỉnh Sửa Thông Tin
                    </a>
                </div>
            </div>
        </div>
        
        <div class="card mt-4">
            <div class="card-header">
                <h5 class="card-title mb-0">Hoạt Động Gần Đây</h5>
            </div>
            <div class="card-body">
                <div class="timeline">
                    <div class="timeline-item">
                        <div class="timeline-marker bg-success"></div>
                        <div class="timeline-content">
                            <h6 class="mb-1">Đăng Nhập Thành Công</h6>
                            <p class="text-muted mb-0">Bạn Đã Đăng Nhập Vào Hệ Thống</p>
                            <small class="text-muted">{{ now()->format('d/m/Y H:i') }}</small>
                        </div>
                    </div>
                    
                    <div class="timeline-item">
                        <div class="timeline-marker bg-info"></div>
                        <div class="timeline-content">
                            <h6 class="mb-1">Tài Khoản Được Tạo</h6>
                            <p class="text-muted mb-0">Tài Khoản Của Bạn Đã Được Tạo Thành Công</p>
                            <small class="text-muted">{{ Auth::user()->created_at->format('d/m/Y H:i') }}</small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-md-4 mb-4">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">Thao Tác Nhanh</h5>
            </div>
            <div class="card-body">
                <div class="d-grid gap-2">
                    <a href="{{ route('user.profile') }}" class="btn btn-primary">
                        Xem Hồ Sơ Cá Nhân
                    </a>
                    <a href="{{ route('profile.edit') }}" class="btn btn-outline-primary">
                        Chỉnh Sửa Thông Tin
                    </a>
                    <button class="btn btn-outline-secondary">
                        Thay Đổi Mật Khẩu
                    </button>
                    <button class="btn btn-outline-info">
                        Liên Hệ Hỗ Trợ
                    </button>
                </div>
            </div>
        </div>
        
        <div class="card mt-4">
            <div class="card-header">
                <h5 class="card-title mb-0">Thông Báo</h5>
            </div>
            <div class="card-body">
                <div class="alert alert-info">
                    <h6 class="alert-heading">Chào Mừng!</h6>
                    <p class="mb-0">Cảm Ơn Bạn Đã Tham Gia Hệ Thống Của Chúng Tôi. Hãy Khám Phá Các Tính Năng Mới.</p>
                </div>
                
                <div class="alert alert-success">
                    <h6 class="alert-heading">Bảo Mật</h6>
                    <p class="mb-0">Tài Khoản Của Bạn Được Bảo Vệ Với Các Biện Pháp Bảo Mật Cao Cấp.</p>
                </div>
            </div>
        </div>
        
        <div class="card mt-4">
            <div class="card-header">
                <h5 class="card-title mb-0">Thống Kê</h5>
            </div>
            <div class="card-body">
                <div class="row text-center">
                    <div class="col-6 mb-3">
                        <div class="border-end">
                            <h4 class="text-primary mb-1">{{ Auth::user()->created_at->diffInDays(now()) }}</h4>
                            <small class="text-muted">Ngày Tham Gia</small>
                        </div>
                    </div>
                    <div class="col-6 mb-3">
                        <h4 class="text-success mb-1">1</h4>
                        <small class="text-muted">Tài Khoản Hoạt Động</small>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Services Section -->
<div class="row mt-5">
    <div class="col-12">
        <h2 class="text-center mb-5">Dịch Vụ Của Chúng Tôi</h2>
    </div>
    
    <div class="col-md-4 mb-4">
        <div class="card text-center h-100">
            <div class="card-body">
                <div class="feature-icon mx-auto mb-3">
                    <i class="fa fa-code"></i>
                </div>
                <h5 class="card-title">Quản Lý Tài Khoản</h5>
                <p class="card-text">Quản Lý Thông Tin Cá Nhân Và Cài Đặt Tài Khoản Một Cách Dễ Dàng.</p>
                <a href="{{ route('user.profile') }}" class="btn btn-primary">Tìm Hiểu Thêm</a>
            </div>
        </div>
    </div>
    
    <div class="col-md-4 mb-4">
        <div class="card text-center h-100">
            <div class="card-body">
                <div class="feature-icon mx-auto mb-3">
                    <i class="fa fa-code"></i>
                </div>
                <h5 class="card-title">Bảo Mật Thông Tin</h5>
                <p class="card-text">Hệ Thống Bảo Mật Tiên Tiến Để Bảo Vệ Thông Tin Cá Nhân Của Bạn.</p>
                <button class="btn btn-primary">Tìm Hiểu Thêm</button>
            </div>
        </div>
    </div>
    
    <div class="col-md-4 mb-4">
        <div class="card text-center h-100">
            <div class="card-body">
                <div class="feature-icon mx-auto mb-3">
                    <i class="fa fa-code"></i>
                </div>
                <h5 class="card-title">Hỗ Trợ Khách Hàng</h5>
                <p class="card-text">Đội Ngũ Hỗ Trợ Chuyên Nghiệp Luôn Sẵn Sàng Giúp Đỡ Bạn 24/7.</p>
                <button class="btn btn-primary">Liên Hệ Ngay</button>
            </div>
        </div>
    </div>
</div>
@endsection

@push('styles')
<style>
    .avatar-large {
        width: 120px;
        height: 120px;
        font-size: 48px;
    }
    
    .timeline {
        position: relative;
        padding-left: 30px;
    }
    
    .timeline::before {
        content: '';
        position: absolute;
        left: 15px;
        top: 0;
        bottom: 0;
        width: 2px;
        background: #e9ecef;
    }
    
    .timeline-item {
        position: relative;
        margin-bottom: 20px;
    }
    
    .timeline-marker {
        position: absolute;
        left: -23px;
        top: 5px;
        width: 12px;
        height: 12px;
        border-radius: 50%;
        border: 2px solid white;
        box-shadow: 0 0 0 2px #e9ecef;
    }
    
    .timeline-content {
        background: #f8f9fa;
        padding: 15px;
        border-radius: 8px;
        border-left: 3px solid #667eea;
    }
</style>
@endpush
