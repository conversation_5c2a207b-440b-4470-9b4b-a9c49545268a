// Tooltip js
"use strict";
$(function() {
    var tooltip_init = {
        init: function () {
            $("button").tooltip();
            $("a").tooltip();
            $("input").tooltip();
            $("li").tooltip();
        }
    };
    tooltip_init.init()
});


$(function() {
    $("#myPopover").popover({
        
    });
});
$(function() {
    $("#myPopover01").popover({
      
    });
});


$(function() {
    $("#myPopover2").popover({
      
    });
});

$(function() {
    $("#myPopover3").popover({
      
    });
});

$(function() {
    $("#myPopover4").popover({
      
    });
});