/*!
 * Muuri v0.5.3
 * https://github.com/haltu/muuri
 * Copyright (c) 2015, <PERSON><PERSON>
 *
 * Permission is hereby granted, free of charge, to any person obtaining a copy
 * of this software and associated documentation files (the "Software"), to deal
 * in the Software without restriction, including without limitation the rights
 * to use, copy, modify, merge, publish, distribute, sublicense, and/or sell
 * copies of the Software, and to permit persons to whom the Software is
 * furnished to do so, subject to the following conditions:
 *
 * The above copyright notice and this permission notice shall be included in
 * all copies or substantial portions of the Software.
 *
 * THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
 * IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
 * FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE
 * AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER
 * LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,
 * OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE
 * SOFTWARE.
 */
/*!
 * muuriLayout v0.5.3
 * Copyright (c) 2016 Niklas Rämö <<EMAIL>>
 * Released under the MIT license
 */
!function(t,e){var i;if("object"==typeof module&&module.exports){try{i=require("hammerjs")}catch(t){}module.exports=e("Muuri",i)}else"function"==typeof define&&define.amd?define(["hammerjs"],function(t){return e("Muuri",t)}):t.Muuri=e("Muuri",t.Hammer)}("undefined"!=typeof window?window:this,function(t,e,i){"use strict";function n(t,e){var i,r,s,o=this;if(J||(J=document.body,mt=P()),t=o._element=typeof t===tt?Z.querySelector(t):t,!J.contains(t))throw new q("Container element must be an existing DOM element");typeof(i=o._settings=function(t,e){var i=p({},t);return i=e?p(i,e):i,i.visibleStyles=(e||{}).visibleStyles||(t||{}).visibleStyles,i.hiddenStyles=(e||{}).hiddenStyles||(t||{}).hiddenStyles,i}(n.defaultOptions,e)).dragSort!==K&&(i.dragSort=!!i.dragSort),ft[o._id=++_t]=o,o._isDestroyed=!1,o._layout=null,o._emitter=new n.Emitter,o._itemShowHandler=M("show",i),o._itemHideHandler=M("hide",i),b(t,i.containerClass),o._items=[],typeof(r=i.items)===tt?x(o._element.children).forEach(function(t){("*"===r||pt(t,r))&&o._items.push(new n.Item(o,t))}):(j.isArray(r)||_(r))&&(o._items=x(r).map(function(t){return new n.Item(o,t)})),(s=!0===(s=i.layoutOnResize)?0:typeof s===et?s:-1)>=0&&V.addEventListener("resize",o._resizeHandler=m(function(){o.refreshItems().layout()},s)),i.layoutOnInit&&o.layout(!0)}function r(t,e){var i,r=t._settings;this._id=++_t,ut[this._id]=this,this._isDestroyed=!1,e.parentNode!==t._element&&t._element.appendChild(e),b(e,r.itemClass),b(e,(i="none"===v(e,"display"))?r.itemHiddenClass:r.itemVisibleClass),this._gridId=t._id,this._element=e,this._child=e.children[0],this._animate=new n.ItemAnimate(this,e),this._animateChild=new n.ItemAnimate(this,this._child),this._isActive=!i,this._isPositioning=!1,this._isHidden=i,this._isHiding=!1,this._isShowing=!1,this._visibilityQueue=[],this._layoutQueue=[],this._left=0,this._top=0,C(e,{left:"0",top:"0",transform:S(0,0),display:i?"none":"block"}),this._refreshDimensions()._refreshSortData(),i?t._itemHideHandler.start(this,!0):t._itemShowHandler.start(this,!0),this._migrate=new n.ItemMigrate(this),this._release=new n.ItemRelease(this),this._drag=r.dragEnabled?new n.ItemDrag(this):null}function s(){this._events={},this._isDestroyed=!1}function o(t,e){this._item=t,this._element=e,this._animation=null,this._propsTo=null,this._isDestroyed=!1}function a(t){this._itemId=t._id,this._isDestroyed=!1,this.isActive=!1,this.container=!1,this.containerDiffX=0,this.containerDiffY=0}function h(t){this._itemId=t._id,this._isDestroyed=!1,this.isActive=!1,this.isPositioningStarted=!1,this.containerDiffX=0,this.containerDiffY=0}function l(i){if(!e)throw new q("["+t+"] required dependency Hammer is not defined.");var n,r,s=this,o=i._element,a=i.getGrid(),h=a._settings,d=typeof h.dragStartPredicate===K?h.dragStartPredicate:l.defaultStartPredicate,f=at;s._itemId=i._id,s._gridId=a._id,s._hammer=n=new e.Manager(o),s._isDestroyed=!1,s._isMigrating=!1,s._data={},s._resolveStartPredicate=function(t){s._isDestroyed||f!==ht||(f=lt,s.onStart(t))},s._scrollListener=function(t){s.onScroll(t)},s._checkSortOverlap=m(function(){s._data.isActive&&s.checkOverlap()},h.dragSortInterval),s._sortPredicate=typeof h.dragSortPredicate===K?h.dragSortPredicate:l.defaultSortPredicate,s.reset(),n.add(new e.Pan({event:"drag",pointers:1,threshold:0,direction:e.DIRECTION_ALL})),n.add(new e.Press({event:"draginit",pointers:1,threshold:1e3,time:0})),c(h.dragHammerSettings)&&n.set(h.dragHammerSettings),n.on("draginit dragstart dragmove",function(t){f===at&&(f=ht),f===ht?!0===(r=d(s.getItem(),t))?(f=lt,s.onStart(t)):!1===r&&(f=dt):f===lt&&s._data.isActive&&s.onMove(t)}).on("dragend dragcancel draginitup",function(t){var e=f===lt;d(s.getItem(),t),f=at,e&&s._data.isActive&&s.onEnd(t)}),o.addEventListener("dragstart",T,!1)}function d(t,e,i){var n=t.length,r=W.max(0,i?n:n-1);return e>r?r:e<0?W.max(r+e+1,0):e}function f(t,e,i){if(!(t.length<2)){var n,r=d(t,e),s=d(t,i);r!==s&&(n=t[r],t[r]=t[s],t[s]=n)}}function u(t,e,i){if(!(t.length<2)){var n=d(t,e),r=d(t,i);n!==r&&t.splice(r,0,t.splice(n,1)[0])}}function c(t){return"object"==typeof t&&"[object Object]"===Q.prototype.toString.call(t)}function _(t){var e=Q.prototype.toString.call(t);return"[object HTMLCollection]"===e||"[object NodeList]"===e}function p(t,e){return Q.keys(e).forEach(function(i){var n=c(e[i]);c(t[i])&&n?(t[i]=p({},t[i]),t[i]=p(t[i],e[i])):t[i]=n?p({},e[i]):j.isArray(e[i])?e[i].concat():e[i]}),t}function g(t,e,i){var n=typeof i===et?i:-1;t.splice.apply(t,[n<0?t.length-n+1:n,0].concat(e))}function m(t,e){var n;return e>0?function(r){n!==i&&(n=V.clearTimeout(n),"finish"===r&&t()),"cancel"!==r&&"finish"!==r&&(n=V.setTimeout(function(){n=i,t()},e))}:function(e){"cancel"!==e&&t()}}function y(t){return t.replace(/([A-Z])/g,"-$1").toLowerCase()}function v(t,e){return V.getComputedStyle(t,null).getPropertyValue("transform"===e?gt.styleName||e:e)}function D(t,e){return parseFloat(v(t,e))||0}function w(t,e){return parseFloat((v(t,"transform")||"").replace("matrix(","").split(",")["x"===e?4:5])||0}function S(t,e){return"translateX("+t+"px) translateY("+e+"px)"}function I(t,e){var i,n={},r=Q.keys(e);for(i=0;i<r.length;i++)n[r[i]]=v(t,y(r[i]));return n}function C(t,e){var i,n=Q.keys(e);for(i=0;i<n.length;i++)t.style["transform"===n[i]&&gt?gt.propName:n[i]]=e[n[i]]}function b(t,e){t.classList?t.classList.add(e):pt(t,"."+e)||(t.className+=" "+e)}function A(t,e){t.classList?t.classList.remove(e):pt(t,"."+e)&&(t.className=(" "+t.className+" ").replace(" "+e+" "," ").trim())}function x(t){return[].slice.call(t)}function H(t,e,i){if(t===e)return{left:0,top:0};i&&(t=R(t,!0),e=R(e,!0));var n=E(t,!0),r=E(e,!0);return{left:r.left-n.left,top:r.top-n.top}}function E(t,e){var i,n={left:0,top:0};return t===Z?n:(n.left=V.pageXOffset||0,n.top=V.pageYOffset||0,t.self===V.self?n:(i=t.getBoundingClientRect(),n.left+=i.left,n.top+=i.top,e&&(n.left+=D(t,"border-left-width"),n.top+=D(t,"border-top-width")),n))}function R(t,e){for(var i=(e?t:t.parentElement)||Z;i&&i!==Z&&"static"===v(i,"position")&&!G(i);)i=i.parentElement||Z;return i}function L(t){var e=[],i=/(auto|scroll)/,n=t.parentNode;if(mt){if("fixed"===v(t,"position"))return e;for(;n&&n!==Z&&n!==$;)i.test(v(n,"overflow")+v(n,"overflow-y")+v(n,"overflow-x"))&&e.push(n),n="fixed"===v(n,"position")?null:n.parentNode;null!==n&&e.push(V)}else{for(;n&&n!==Z;)"fixed"!==v(t,"position")||G(n)?(i.test(v(n,"overflow")+v(n,"overflow-y")+v(n,"overflow-x"))&&e.push(n),t=n,n=n.parentNode):n=n.parentNode;e[e.length-1]===$?e[e.length-1]=V:e.push(V)}return e}function P(){if(!gt)return!0;var t=[0,1].map(function(t,e){return t=Z.createElement("div"),C(t,{position:e?"fixed":"absolute",display:"block",visibility:"hidden",left:e?"0px":"1px",transform:"none"}),t}),e=J.appendChild(t[0]),i=e.appendChild(t[1]),n=i.getBoundingClientRect().left;C(e,{transform:"scale(1)"});var r=n===i.getBoundingClientRect().left;return J.removeChild(e),r}function G(t){var e=v(t,"transform"),i=v(t,"display");return"none"!==e&&"inline"!==i&&"none"!==i}function X(t,e){if(!B.doRectsOverlap(t,e))return 0;return(W.min(t.left+t.width,e.left+e.width)-W.max(t.left,e.left))*(W.min(t.top+t.height,e.top+e.height)-W.max(t.top,e.top))/(W.min(t.width,e.width)*W.min(t.height,e.height))*100}function Y(t){var e,i={};for(e=0;e<t.length;e++)i[t[e]._id]=e;return i}function O(t,e,i,n){var r=n[t._id],s=n[e._id];return i?s-r:r-s}function k(t,e,n,r){var s,o,a=t.getItems(n),h=r||{},l=!0===h.instant,d=h.onFinish,f=h.layout?h.layout:h.layout===i,u=a.length,c="show"===e,_=c?vt:wt,p=c?Dt:St,g=!1,m=[],y=[];if(u){for(t._emit(_,a.concat()),o=0;o<a.length;o++)s=a[o],(c&&!s._isActive||!c&&s._isActive)&&(g=!0),c&&!s._isActive&&(s._skipNextLayoutAnimation=!0),c&&s._isHidden&&y.push(s),s["_"+e](l,function(e,i){e||m.push(i),--u<1&&(typeof d===K&&d(m.concat()),t._emit(p,m.concat()))});y.length&&t.refreshItems(y),g&&f&&t.layout("instant"===f,typeof f===K?f:i)}else typeof d===K&&d(a);return t}function M(t,e){var i,n="show"===t,r=parseInt(n?e.showDuration:e.hideDuration)||0,s=(n?e.showEasing:e.hideEasing)||"ease",o=n?e.visibleStyles:e.hiddenStyles,a=r>0;return o=c(o)?o:null,{start:function(t,e,n){o?(it.cancel(rt,t._id),!a||e?(t._animateChild.isAnimating()?t._animateChild.stop(o):C(t._child,o),n&&n()):it.add(rt,t._id,function(){i=I(t._child,o)},function(){t._animateChild.start(i,o,{duration:r,easing:s,onFinish:n})})):n&&n()},stop:function(t,e){it.cancel(rt,t._id),t._animateChild.stop(e)}}}function N(t,e,i){var n,r=t.splice(0,t.length);for(n=0;n<r.length;n++)r[n](e,i)}function T(t){t.preventDefault&&t.preventDefault()}function z(t,e){var i,n=t._drag._startPredicateData,r=e.changedPointers[0],s=r&&r.pageX||0,o=r&&r.pageY||0;if(!(e.distance<n.distance||n.delay))return i=n.handleElement.getBoundingClientRect(),F(t),function(t,e,i){return i.width&&i.height&&t>=i.left&&t<i.left+i.width&&e>=i.top&&e<i.top+i.height}(s,o,{width:i.width,height:i.height,left:i.left+(V.pageXOffset||0),top:i.top+(V.pageYOffset||0)})}function F(t){var e=t._drag._startPredicateData;e&&(e.delayTimer&&(e.delayTimer=V.clearTimeout(e.delayTimer)),t._drag._startPredicateData=null)}function B(t,e,i,n){var r,s,o,a,h,l,d,f=!!n.fillGaps,u=!!n.horizontal,c=!!n.alignRight,_=!!n.alignBottom,p=!!n.rounding,g={slots:{},width:u?0:p?W.round(e):e,height:u?p?W.round(i):i:0,setWidth:u,setHeight:!u},m=[];if(!t.length)return g;for(d=0;d<t.length;d++)h=(a=t[d])._width+a._margin.left+a._margin.right,l=a._height+a._margin.top+a._margin.bottom,p&&(h=W.round(h),l=W.round(l)),o=(s=B.getSlot(g,m,h,l,!u,f))[0],m=s[1],u?g.width=W.max(g.width,o.left+o.width):g.height=W.max(g.height,o.top+o.height),g.slots[a._id]=o;if(c||_)for(r=Q.keys(g.slots),d=0;d<r.length;d++)o=g.slots[r[d]],c&&(o.left=g.width-(o.left+o.width)),_&&(o.top=g.height-(o.top+o.height));return g}var V=window,Q=V.Object,j=V.Array,W=V.Math,q=V.Error,U=V.Element,Z=V.document,$=Z.documentElement,J=Z.body,K="function",tt="string",et="number",it=function(){function t(){var a,h=+n._maxRafBatchSize||100,l=r.splice(0,W.min(h,r.length)),d={};for(e=null,a=0;a<l.length;a++)d[l[a]]=s[l[a]],s[l[a]]=i;for(a=0;a<l.length;a++)d[l[a]][0]();for(a=0;a<l.length;a++)d[l[a]][1]();!e&&r.length&&(e=o(t))}var e=null,r=[],s={},o=(V.requestAnimationFrame||V.webkitRequestAnimationFrame||V.mozRequestAnimationFrame||V.msRequestAnimationFrame||function(t){return V.setTimeout(t,16)}).bind(V);return{add:function(i,n,a,h){var l=r.indexOf(i+n);l>-1&&r.splice(l,1),i===st||i===ot?r.unshift(i+n):r.push(i+n),s[i+n]=[a,h],!e&&(e=o(t))},cancel:function(t,e){var n=r.indexOf(t+e);n>-1&&(r.splice(n,1),s[t+e]=i)}}}(),nt="layout",rt="visibility",st="move",ot="scroll",at=0,ht=1,lt=2,dt=3,ft={},ut={},ct=function(){},_t=0,pt=function(){var t=U.prototype,e=t.matches||t.matchesSelector||t.webkitMatchesSelector||t.mozMatchesSelector||t.msMatchesSelector||t.oMatchesSelector;return function(t,i){return e.call(t,i)}}(),gt=function(t){var e,n,r,s=t.charAt(0).toUpperCase()+t.slice(1),o=["","Webkit","Moz","O","ms"];for(r=0;r<o.length;r++)if(e=o[r],n=e?e+s:t,$.style[n]!==i)return e=e.toLowerCase(),{prefix:e,propName:n,styleName:e?"-"+e+"-"+t:t};return null}("transform"),mt=J?P():null,yt="layoutEnd",vt="showStart",Dt="showEnd",wt="hideStart",St="hideEnd",It="beforeSend",Ct="beforeReceive";return n.Item=r,n.ItemDrag=l,n.ItemRelease=h,n.ItemMigrate=a,n.ItemAnimate=o,n.Layout=function(t,e){var i=t._settings.layout;e=e.concat(),t._refreshDimensions();var n=t._width-t._border.left-t._border.right,r=t._height-t._border.top-t._border.bottom,s=typeof i===K?i(e,n,r):B(e,n,r,c(i)?i:{});this.slots=s.slots,this.setWidth=s.setWidth||!1,this.setHeight=s.setHeight||!1,this.width=s.width,this.height=s.height},n.Emitter=s,n.defaultOptions={items:"*",showDuration:300,showEasing:"ease",hideDuration:300,hideEasing:"ease",visibleStyles:{opacity:"1",transform:"scale(1)"},hiddenStyles:{opacity:"0",transform:"scale(0.5)"},layout:{fillGaps:!1,horizontal:!1,alignRight:!1,alignBottom:!1,rounding:!0},layoutOnResize:100,layoutOnInit:!0,layoutDuration:300,layoutEasing:"ease",sortData:null,dragEnabled:!1,dragContainer:null,dragStartPredicate:{distance:0,delay:0,handle:!1},dragAxis:null,dragSort:!0,dragSortInterval:100,dragSortPredicate:{threshold:50,action:"move"},dragReleaseDuration:300,dragReleaseEasing:"ease",dragHammerSettings:{touchAction:"none"},containerClass:"muuri",itemClass:"muuri-item",itemVisibleClass:"muuri-item-shown",itemHiddenClass:"muuri-item-hidden",itemPositioningClass:"muuri-item-positioning",itemDraggingClass:"muuri-item-dragging",itemReleasingClass:"muuri-item-releasing"},n._maxRafBatchSize=100,n.prototype.on=function(t,e){return this._isDestroyed||this._emitter.on(t,e),this},n.prototype.once=function(t,e){return this._isDestroyed||this._emitter.once(t,e),this},n.prototype.off=function(t,e){return this._isDestroyed||this._emitter.off(t,e),this},n.prototype.getElement=function(){return this._element},n.prototype.getItems=function(t,e){if(this._isDestroyed)return[];var i,n,r=0===t||t&&typeof t!==tt,s=r?_(t)?x(t):[].concat(t):null,o=r?e:t,a=[];if((o=typeof o===tt?o:null)||s){for(s=s||this._items,n=0;n<s.length;n++)!(i=r?this._getItem(s[n]):s[n])||o&&!function(t,e){var i;return"inactive"===e?!t.isActive():"hidden"===e?!t.isVisible():(i="is"+e.charAt(0).toUpperCase()+e.slice(1),typeof t[i]===K&&t[i]())}(i,o)||a.push(i);return a}return a.concat(this._items)},n.prototype.refreshItems=function(t){if(this._isDestroyed)return this;var e,i=this.getItems(t||"active");for(e=0;e<i.length;e++)i[e]._refreshDimensions();return this},n.prototype.refreshSortData=function(t){if(this._isDestroyed)return this;var e,i=this.getItems(t);for(e=0;e<i.length;e++)i[e]._refreshSortData();return this},n.prototype.synchronize=function(){if(this._isDestroyed)return this;var t,e,i,n=this._element,r=this._items;if(r.length){for(i=0;i<r.length;i++)(e=r[i]._element).parentNode===n&&(t=t||Z.createDocumentFragment()).appendChild(e);t&&n.appendChild(t)}return this._emit("synchronize"),this},n.prototype.layout=function(t,e){function i(){--_<=0&&(typeof d===K&&d(r._layout!==c,u.concat()),r._layout===c&&r._emit(yt,u.concat()))}var r=this;if(r._isDestroyed)return r;var s,o,a,h,l,d=typeof t===K?t:e,f=!0===t,u=r.getItems("active"),c=r._layout=new n.Layout(r,u),_=u.length;if((c.setWidth||c.setHeight)&&(o={},s="border-box"===v(r._element,"box-sizing"),c.setHeight&&(typeof c.height===et?o.height=(s?c.height+r._border.top+r._border.bottom:c.height)+"px":o.height=c.height),c.setWidth&&(typeof c.width===et?o.width=(s?c.width+r._border.left+r._border.right:c.width)+"px":o.width=c.width),C(r._element,o)),r._emit("layoutStart",u.concat()),!u.length)return i(),r;for(l=0;l<u.length;l++)a=u[l],h=c.slots[a._id],a._left=h.left,a._top=h.top,a.isDragging()?i():a._layout(f,i);return r},n.prototype.add=function(t,e){if(this._isDestroyed)return[];var r=_(t)?x(t):[].concat(t),s=[];if(!r.length)return s;var o,a,h,l=e||{},d=l.layout?l.layout:l.layout===i,f=this._items,u=!1;for(h=0;h<f.length;h++)if((o=r.indexOf(f[h]._element))>-1&&(r.splice(o,1),!r.length))return s;for(h=0;h<r.length;h++)a=new n.Item(this,r[h]),s.push(a),a._isActive&&(u=!0,a._skipNextLayoutAnimation=!0);return g(f,s,l.index),this._emit("add",s.concat()),u&&d&&this.layout("instant"===d,typeof d===K?d:i),s},n.prototype.remove=function(t,e){if(this._isDestroyed)return this;var n,r,s=e||{},o=s.layout?s.layout:s.layout===i,a=!1,h=this.getItems(t);for(r=0;r<h.length;r++)(n=h[r])._isActive&&(a=!0),n._destroy(s.removeElements);return this._emit("remove",h.concat()),a&&o&&this.layout("instant"===o,typeof o===K?o:i),h},n.prototype.show=function(t,e){return this._isDestroyed?this:k(this,"show",t,e)},n.prototype.hide=function(t,e){return this._isDestroyed?this:k(this,"hide",t,e)},n.prototype.filter=function(t,e){if(this._isDestroyed||!this._items.length)return this;var n,r,s=this._items,o=typeof t,a=o===K,h=e||{},l=!0===h.instant,d=h.layout?h.layout:h.layout===i,f=typeof h.onFinish===K?h.onFinish:null,u=[],c=[],_=-1,p=f?function(){++_&&f(u.concat(),c.concat())}:ct;if(a||o===tt)for(r=0;r<s.length;r++)n=s[r],(a?t(n):pt(n._element,t))?u.push(n):c.push(n);return u.length?this.show(u,{instant:l,onFinish:p,layout:!1}):p(),c.length?this.hide(c,{instant:l,onFinish:p,layout:!1}):p(),(u.length||c.length)&&(this._emit("filter",u.concat(),c.concat()),d&&this.layout("instant"===d,typeof d===K?d:i)),this},n.prototype.sort=function(t,e){if(this._isDestroyed||this._items.length<2)return this;var n,r=this._items,s=e||{},o=!!s.descending,a=s.layout?s.layout:s.layout===i,h=r.concat();if(typeof t===K)r.sort(function(e,i){var r=t(e,i);return(o&&0!==r?-r:r)||O(e,i,o,n||(n=Y(h)))});else if(typeof t===tt)t=t.trim().split(" ").map(function(t){return t.split(":")}),r.sort(function(e,i){return function(t,e,i,n){var r,s,o,a,h,l=0;for(h=0;h<n.length;h++)if(r=n[h][0],s=n[h][1],o=(t._sortData?t:t._refreshSortData())._sortData[r],a=(e._sortData?e:e._refreshSortData())._sortData[r],0!=(l="desc"===s||!s&&i?a<o?-1:a>o?1:0:o<a?-1:o>a?1:0))return l;return l}(e,i,o,t)||O(e,i,o,n||(n=Y(h)))});else{if(!j.isArray(t))return this;!function(t,e){var i,n,r,s=[],o=t.concat();for(r=0;r<e.length;r++)i=e[r],(n=o.indexOf(i))>-1&&(s.push(i),o.splice(n,1));t.splice.apply(t,[0,t.length].concat(s).concat(o))}(r,t),o&&r.reverse()}return this._emit("sort",r.concat(),h),a&&this.layout("instant"===a,typeof a===K?a:i),this},n.prototype.move=function(t,e,n){if(this._isDestroyed||this._items.length<2)return this;var r,s,o=this._items,a=n||{},h=a.layout?a.layout:a.layout===i,l="swap"===a.action,d=l?"swap":"move",c=this._getItem(t),_=this._getItem(e);return c&&_&&c!==_&&(r=o.indexOf(c),s=o.indexOf(_),(l?f:u)(o,r,s),this._emit("move",{item:c,fromIndex:r,toIndex:s,action:d}),h&&this.layout("instant"===h,typeof h===K?h:i)),this},n.prototype.send=function(t,e,n,r){if(this._isDestroyed||e._isDestroyed||this===e||!(t=this._getItem(t)))return this;var s=e,o=r||{},a=o.appendTo||J,h=o.layoutSender?o.layoutSender:o.layoutSender===i,l=o.layoutReceiver?o.layoutReceiver:o.layoutReceiver===i;return t._migrate.start(s,n,a),t._migrate.isActive&&t.isActive()&&(h&&this.layout("instant"===h,typeof h===K?h:i),l&&s.layout("instant"===l,typeof l===K?l:i)),this},n.prototype.destroy=function(t){if(this._isDestroyed)return this;var e,n=this._element,r=this._items.concat();for(this._resizeHandler&&V.removeEventListener("resize",this._resizeHandler),e=0;e<r.length;e++)r[e]._destroy(t);return A(n,this._settings.containerClass),C(n,{height:""}),this._emit("destroy"),this._emitter.destroy(),ft[this._id]=i,this._isDestroyed=!0,this},n.prototype._getItem=function(t){var e,i=this._items;if(this._isDestroyed||!t)return i[0]||null;if(typeof t===et)return i[t>-1?t:i.length+t]||null;if(t instanceof r)return t._gridId===this._id?t:null;for(e=0;e<i.length;e++)if(i[e]._element===t)return i[e];return null},n.prototype._emit=function(){return this._isDestroyed||this._emitter.emit.apply(this._emitter,arguments),this},n.prototype._refreshDimensions=function(){var t,e=this._element,i=e.getBoundingClientRect(),n=["left","right","top","bottom"];for(this._width=i.width,this._height=i.height,this._left=i.left,this._top=i.top,this._border={},t=0;t<n.length;t++)this._border[n[t]]=D(e,"border-"+n[t]+"-width");return this},r.prototype.getGrid=function(){return ft[this._gridId]},r.prototype.getElement=function(){return this._element},r.prototype.getWidth=function(){return this._width},r.prototype.getHeight=function(){return this._height},r.prototype.getMargin=function(){return{left:this._margin.left,right:this._margin.right,top:this._margin.top,bottom:this._margin.bottom}},r.prototype.getPosition=function(){return{left:this._left,top:this._top}},r.prototype.isActive=function(){return this._isActive},r.prototype.isVisible=function(){return!this._isHidden},r.prototype.isShowing=function(){return this._isShowing},r.prototype.isHiding=function(){return this._isHiding},r.prototype.isPositioning=function(){return this._isPositioning},r.prototype.isDragging=function(){return!!this._drag&&this._drag._data.isActive},r.prototype.isReleasing=function(){return this._release.isActive},r.prototype.isDestroyed=function(){return this._isDestroyed},r.prototype._refreshDimensions=function(){if(this._isDestroyed||this._isHidden)return this;var t,e,i=this._element,n=i.getBoundingClientRect(),r=["left","right","top","bottom"],s=this._margin=this._margin||{};for(this._width=n.width,this._height=n.height,e=0;e<4;e++)t=D(i,"margin-"+r[e]),s[r[e]]=t>0?t:0;return this},r.prototype._refreshSortData=function(){var t=this;if(t._isDestroyed)return t;var e={},i=t.getGrid()._settings.sortData;return i&&Q.keys(i).forEach(function(n){e[n]=i[n](t,t._element)}),t._sortData=e,t},r.prototype._layout=function(t,e){var i=this;if(i._isDestroyed)return i;var n,r,s,o,a,h,l=i._element,d=i._isPositioning,f=i._migrate,u=i._release,c=u.isActive&&!1===u.isPositioningStarted,_=i.getGrid()._settings,p=c?_.dragReleaseDuration:_.layoutDuration,g=c?_.dragReleaseEasing:_.layoutEasing,m=!t&&!i._skipNextLayoutAnimation&&p>0;return d&&N(i._layoutQueue,!0,i),c&&(u.isPositioningStarted=!0),typeof e===K&&i._layoutQueue.push(e),r=u.isActive?u.containerDiffX:f.isActive?f.containerDiffX:0,s=u.isActive?u.containerDiffY:f.isActive?f.containerDiffY:0,h={transform:S(i._left+r,i._top+s)},m?(i._isPositioning=!0,it.add(nt,i._id,function(){o=w(l,"x")-r,a=w(l,"y")-s},function(){if(i._left===o&&i._top===a)return d&&i._stopLayout(!1,h),i._isPositioning=!1,i._finishLayout();!d&&b(l,_.itemPositioningClass),i._animate.start({transform:S(o+r,a+s)},h,{duration:p,easing:g,onFinish:function(){i._finishLayout()}})}),i):(d&&it.cancel(nt,i._id),n=i._animate.isAnimating(),i._stopLayout(!1,h),!n&&C(l,h),i._skipNextLayoutAnimation=!1,i._finishLayout())},r.prototype._finishLayout=function(){return this._isDestroyed?this:(this._isPositioning&&(this._isPositioning=!1,A(this._element,this.getGrid()._settings.itemPositioningClass)),this._release.isActive&&this._release.stop(),this._migrate.isActive&&this._migrate.stop(),N(this._layoutQueue,!1,this),this)},r.prototype._stopLayout=function(t,e){return this._isDestroyed||!this._isPositioning?this:(it.cancel(nt,this._id),this._animate.stop(e),A(this._element,this.getGrid()._settings.itemPositioningClass),this._isPositioning=!1,t&&N(this._layoutQueue,!0,this),this)},r.prototype._show=function(t,e){var i=this;if(i._isDestroyed)return i;var n=i._element,r=i._visibilityQueue,s=typeof e===K?e:null,o=i.getGrid(),a=o._settings;return i._isShowing||i._isHidden?i._isShowing&&!t?(s&&r.push(s),i):(i._isShowing||(N(r,!0,i),A(n,a.itemHiddenClass),b(n,a.itemVisibleClass),!i._isHiding&&C(n,{display:"block"})),s&&r.push(s),i._isActive=i._isShowing=!0,i._isHiding=i._isHidden=!1,t?(o._itemShowHandler.stop(i,a.visibleStyles),i._isShowing=!1,N(r,!1,i)):o._itemShowHandler.start(i,t,function(){i._isHidden||(i._isShowing=!1,N(r,!1,i))}),i):(s&&s(!1,i),i)},r.prototype._hide=function(t,e){var i=this;if(i._isDestroyed)return i;var n=i._element,r=i._visibilityQueue,s=typeof e===K?e:null,o=i.getGrid(),a=o._settings;return!i._isHiding&&i._isHidden?(s&&s(!1,i),i):i._isHiding&&!t?(s&&r.push(s),i):(i._isHiding||(N(r,!0,i),b(n,a.itemHiddenClass),A(n,a.itemVisibleClass)),s&&r.push(s),i._isHidden=i._isHiding=!0,i._isActive=i._isShowing=!1,t?(o._itemHideHandler.stop(i,a.hiddenStyles),i._isHiding=!1,i._stopLayout(!0,{transform:S(0,0)}),C(n,{display:"none"}),N(r,!1,i)):o._itemHideHandler.start(i,t,function(){i._isHidden&&(i._isHiding=!1,i._stopLayout(!0,{transform:S(0,0)}),C(n,{display:"none"}),N(r,!1,i))}),i)},r.prototype._destroy=function(t){if(this._isDestroyed)return this;var e=this._element,n=this.getGrid(),r=n._settings,s=n._items.indexOf(this);return this._release.destroy(),this._migrate.destroy(),this._stopLayout(!0,{}),n._itemShowHandler.stop(this,{}),n._itemHideHandler.stop(this,{}),this._drag&&this._drag.destroy(),this._animate.destroy(),this._animateChild.destroy(),N(this._visibilityQueue,!0,this),e.removeAttribute("style"),this._child.removeAttribute("style"),A(e,r.itemPositioningClass),A(e,r.itemDraggingClass),A(e,r.itemReleasingClass),A(e,r.itemClass),A(e,r.itemVisibleClass),A(e,r.itemHiddenClass),s>-1&&n._items.splice(s,1),t&&e.parentNode.removeChild(e),ut[this._id]=i,this._isActive=this._isPositioning=this._isHiding=this._isShowing=!1,this._isDestroyed=this._isHidden=!0,this},s.prototype.on=function(t,e){if(this._isDestroyed)return this;var i=this._events[t]||[];return i.push(e),this._events[t]=i,this},s.prototype.once=function(t,e){var i=this;return this.on(t,function n(){i.off(t,n),e.apply(null,arguments)})},s.prototype.off=function(t,e){if(this._isDestroyed)return this;for(var i=this._events[t]||[],n=i.length;n--;)e===i[n]&&i.splice(n,1);return this},s.prototype.emit=function(t,e,i,n){if(this._isDestroyed)return this;var r,s=this._events[t]||[],o=s.length,a=arguments.length-1;if(o)for(s=s.concat(),r=0;r<o;r++)0===a?s[r]():1===a?s[r](e):2===a?s[r](e,i):s[r](e,i,n);return this},s.prototype.destroy=function(){if(this._isDestroyed)return this;var t,e=Q.keys(this._events);for(t=0;t<e.length;t++)this._events[e[t]]=null;return this._isDestroyed=!0,this},o.prototype.start=function(t,e,i){var n=this;if(!n._isDestroyed){var r=i||{},s=typeof r.onFinish===K?r.onFinish:null;if(n._animation){if(!Q.keys(e).some(function(t){return e[t]!==n._propsTo[t]}))return void(n._animation.onfinish=function(){n._animation=n._propsTo=null,s&&s()});n._animation.cancel()}n._propsTo=e,n._animation=n._element.animate([t,e],{duration:r.duration||300,easing:r.easing||"ease"}),n._animation.onfinish=function(){n._animation=n._propsTo=null,s&&s()},C(n._element,e)}},o.prototype.stop=function(t){!this._isDestroyed&&this._animation&&(C(this._element,t||I(this._element,this._propsTo)),this._animation.cancel(),this._animation=this._propsTo=null)},o.prototype.isAnimating=function(){return!!this._animation},o.prototype.destroy=function(){this._isDestroyed||(this.stop(),this._item=this._element=null,this._isDestroyed=!0)},a.prototype.destroy=function(){return this._isDestroyed||(this.stop(!0),this._isDestroyed=!0),this},a.prototype.getItem=function(){return ut[this._itemId]||null},a.prototype.start=function(t,e,r){if(this._isDestroyed)return this;var s,o,a,h,l,f=this.getItem(),u=f._element,c=f.isVisible(),_=f.getGrid(),p=_._settings,m=t._settings,y=t._element,v=_._items.indexOf(f),D=typeof e===et?e:t._items.indexOf(t._getItem(e)),I=r||J;return null===D?this:(D=d(t._items,D,!0),(f.isPositioning()||this.isActive||f.isReleasing())&&(h=w(u,"x"),l=w(u,"y")),f.isPositioning()&&f._stopLayout(!0,{transform:S(h,l)}),this.isActive&&(h-=this.containerDiffX,l-=this.containerDiffY,this.stop(!0,{transform:S(h,l)})),f.isReleasing()&&(h-=f._release.containerDiffX,l-=f._release.containerDiffY,f._release.stop(!0,{transform:S(h,l)})),_._itemShowHandler.stop(f),_._itemHideHandler.stop(f),f._drag&&f._drag.destroy(),f._animate.destroy(),f._animateChild.destroy(),N(f._visibilityQueue,!0,f),_._emit(It,{item:f,fromGrid:_,fromIndex:v,toGrid:t,toIndex:D}),t._emit(Ct,{item:f,fromGrid:_,fromIndex:v,toGrid:t,toIndex:D}),A(u,p.itemClass),A(u,p.itemVisibleClass),A(u,p.itemHiddenClass),b(u,m.itemClass),b(u,c?m.itemVisibleClass:m.itemHiddenClass),_._items.splice(v,1),g(t._items,f,D),f._gridId=t._id,f._animate=new n.ItemAnimate(f,u),f._animateChild=new n.ItemAnimate(f,f._child),s=u.parentNode,I!==s&&(I.appendChild(u),o=H(I,s,!0),h===i&&(h=w(u,"x"),l=w(u,"y")),C(u,{transform:S(h+o.left,l+o.top)})),f._child.removeAttribute("style"),c?t._itemShowHandler.start(f,!0):t._itemHideHandler.start(f,!0),C(u,{display:c?"block":"hidden"}),a=H(I,y,!0),f._refreshDimensions()._refreshSortData(),f._drag=m.dragEnabled?new n.ItemDrag(f):null,this.isActive=!0,this.container=I,this.containerDiffX=a.left,this.containerDiffY=a.top,_._emit("send",{item:f,fromGrid:_,fromIndex:v,toGrid:t,toIndex:D}),t._emit("receive",{item:f,fromGrid:_,fromIndex:v,toGrid:t,toIndex:D}),this)},a.prototype.stop=function(t,e){if(this._isDestroyed||!this.isActive)return this;var i=this.getItem(),n=i._element,r=i.getGrid()._element;return this.container!==r&&(e||(e={transform:S(t?w(n,"x")-this.containerDiffX:i._left,t?w(n,"y")-this.containerDiffY:i._top)}),r.appendChild(n),C(n,e)),this.isActive=!1,this.container=null,this.containerDiffX=0,this.containerDiffY=0,this},h.prototype.destroy=function(){return this._isDestroyed||(this.stop(!0),this._isDestroyed=!0),this},h.prototype.getItem=function(){return ut[this._itemId]||null},h.prototype.reset=function(){if(this._isDestroyed)return this;var t=this.getItem();return this.isActive=!1,this.isPositioningStarted=!1,this.containerDiffX=0,this.containerDiffY=0,A(t._element,t.getGrid()._settings.itemReleasingClass),this},h.prototype.start=function(){if(this._isDestroyed||this.isActive)return this;var t=this.getItem(),e=t.getGrid();return this.isActive=!0,b(t._element,e._settings.itemReleasingClass),e._emit("dragReleaseStart",t),t._layout(!1),this},h.prototype.stop=function(t,e){if(this._isDestroyed||!this.isActive)return this;var i=this.getItem(),n=i._element,r=i.getGrid(),s=r._element,o=this.containerDiffX,a=this.containerDiffY;return this.reset(),n.parentNode!==s&&(e||(e={transform:S(t?w(n,"x")-o:i._left,t?w(n,"y")-a:i._top)}),s.appendChild(n),C(n,e)),t||r._emit("dragReleaseEnd",i),this},l.defaultStartPredicate=function(t,e,i){var n,r,s,o,a=t._element,h=t._drag._startPredicateData;if(h||(n=c(n=i||t._drag.getGrid()._settings.dragStartPredicate)?n:{},h=t._drag._startPredicateData={distance:W.abs(n.distance)||0,delay:W.max(n.delay,0)||0,handle:"string"==typeof n.handle&&n.handle}),e.isFinal)return r="a"===a.tagName.toLowerCase(),s=a.getAttribute("href"),o=a.getAttribute("target"),F(t),void(r&&s&&W.abs(e.deltaX)<2&&W.abs(e.deltaY)<2&&e.deltaTime<200&&(o&&"_self"!==o?V.open(s,o):V.location.href=s));if(!h.handleElement)if(h.handle){for(h.handleElement=(e.changedPointers[0]||{}).target;h.handleElement&&!pt(h.handleElement,h.handle);)h.handleElement=h.handleElement!==a?h.handleElement.parentElement:null;if(!h.handleElement)return!1}else h.handleElement=a;return h.delay&&(h.event=e,h.delayTimer||(h.delayTimer=V.setTimeout(function(){h.delay=0,z(t,h.event)&&(t._drag._resolveStartPredicate(h.event),F(t))},h.delay))),z(t,e)},l.defaultSortPredicate=function(t){var e,i,n,r,s,o=t._drag,a=o._data,h=o.getGrid(),l=h._settings.dragSortPredicate||{},d=l.threshold||50,f=l.action||"move",u={width:t._width,height:t._height,left:a.elementClientX,top:a.elementClientY},c=function(t,e,i,n){var r,s,o,a=null,h=e._settings.dragSort,l=!0===h?[e]:h.call(e,t),d=-1;if(!j.isArray(l))return a;for(o=0;o<l.length;o++)(s=l[o])._isDestroyed||(s._refreshDimensions(),(r=X(i,{width:s._width,height:s._height,left:s._left,top:s._top}))>n&&r>d&&(d=r,a=s));return a}(t,h,u,d),_=0,p=0,g=-1;if(!c)return!1;for(c===h?(u.left=a.gridX+t._margin.left,u.top=a.gridY+t._margin.top):(_=c._left+c._border.left,p=c._top+c._border.top),s=0;s<c._items.length;s++)(n=c._items[s])._isActive&&n!==t&&(i=!0,(r=X(u,{width:n._width,height:n._height,left:n._left+n._margin.left+_,top:n._top+n._margin.top+p}))>g&&(e=s,g=r));return g<d&&t.getGrid()!==c&&(e=i?-1:0,g=1/0),g>=d&&{grid:c,index:e,action:f}},l.prototype.destroy=function(){return this._isDestroyed||(this.stop(),this._hammer.destroy(),this.getItem()._element.removeEventListener("dragstart",T,!1),this._isDestroyed=!0),this},l.prototype.getItem=function(){return ut[this._itemId]||null},l.prototype.getGrid=function(){return ft[this._gridId]||null},l.prototype.reset=function(){var t=this._data;return t.isActive=!1,t.container=null,t.containingBlock=null,t.startEvent=null,t.currentEvent=null,t.scrollers=[],t.left=0,t.top=0,t.gridX=0,t.gridY=0,t.elementClientX=0,t.elementClientY=0,t.containerDiffX=0,t.containerDiffY=0,this},l.prototype.bindScrollListeners=function(){var t,e=this.getGrid()._element,i=this._data.container,n=L(this.getItem()._element);for(i!==e&&(n=function(t){var e,i=[],n=t.length;if(n)for(i[0]=t[0],e=1;e<n;e++)i.indexOf(t[e])<0&&i.push(t[e]);return i}(n.concat(e).concat(L(e)))),t=0;t<n.length;t++)n[t].addEventListener("scroll",this._scrollListener);return this._data.scrollers=n,this},l.prototype.unbindScrollListeners=function(){var t,e=this._data,i=e.scrollers;for(t=0;t<i.length;t++)i[t].removeEventListener("scroll",this._scrollListener);return e.scrollers=[],this},l.prototype.checkOverlap=function(){var t,e,i,n,r,s,o=this.getItem(),a=this._sortPredicate(o,this._data.currentEvent);return c(a)&&typeof a.index===et?(t=o.getGrid(),i=a.grid||t,s=t!==i,e=t._items.indexOf(o),n=d(i._items,a.index,s),r="swap"===a.action?"swap":"move",s?(t._emit(It,{item:o,fromGrid:t,fromIndex:e,toGrid:i,toIndex:n}),i._emit(Ct,{item:o,fromGrid:t,fromIndex:e,toGrid:i,toIndex:n}),o._gridId=i._id,this._isMigrating=o._gridId!==this._gridId,t._items.splice(e,1),g(i._items,o,n),o._sortData=null,t._emit("send",{item:o,fromGrid:t,fromIndex:e,toGrid:i,toIndex:n}),i._emit("receive",{item:o,fromGrid:t,fromIndex:e,toGrid:i,toIndex:n}),t.layout(),i.layout()):e!==n&&(("swap"===r?f:u)(t._items,e,n),t._emit("move",{item:o,fromIndex:e,toIndex:n,action:r}),t.layout()),this):this},l.prototype.finishMigration=function(){var t,e,i,r=this.getItem(),s=r._release,o=r._element,a=r.getGrid(),h=a._element,l=a._settings,d=l.dragContainer||h,f=this.getGrid()._settings,u=o.parentNode;return this._isMigrating=!1,this.destroy(),r._animate.destroy(),r._animateChild.destroy(),A(o,f.itemClass),A(o,f.itemVisibleClass),A(o,f.itemHiddenClass),b(o,l.itemClass),b(o,l.itemVisibleClass),r._animate=new n.ItemAnimate(r,o),r._animateChild=new n.ItemAnimate(r,r._child),d!==u&&(d.appendChild(o),i=H(u,d,!0),t=w(o,"x")-i.left,e=w(o,"y")-i.top),r._refreshDimensions()._refreshSortData(),i=H(d,h,!0),s.containerDiffX=i.left,s.containerDiffY=i.top,r._drag=l.dragEnabled?new n.ItemDrag(r):null,d!==u&&C(o,{transform:S(t,e)}),r._child.removeAttribute("style"),a._itemShowHandler.start(r,!0),s.start(),this},l.prototype.cancelRafLoop=function(){var t=this.getItem()._id;return it.cancel(ot,t),it.cancel(st,t),this},l.prototype.stop=function(){var t=this._data,e=this.getItem()._element,i=this.getGrid();return t.isActive?this._isMigrating?this.finishMigration(t.currentEvent):(this.cancelRafLoop(),this.unbindScrollListeners(),this._checkSortOverlap("cancel"),e.parentNode!==i._element&&(i._element.appendChild(e),C(e,{transform:S(t.gridX,t.gridY)})),A(e,i._settings.itemDraggingClass),this.reset(),this):this},l.prototype.onStart=function(t){var e=this.getItem();if(!e._isActive)return this;var i=e._element,n=this.getGrid(),r=n._settings,s=this._data,o=e._release,a=e._migrate,h=n._element,l=r.dragContainer||h,d=R(l,!0),f=l!==h?H(d,h):0,u=w(i,"x"),c=w(i,"y"),_=i.getBoundingClientRect();return e.isPositioning()&&e._stopLayout(!0,{transform:S(u,c)}),a.isActive&&(u-=a.containerDiffX,c-=a.containerDiffY,a.stop(!0,{transform:S(u,c)})),e.isReleasing()&&o.reset(),s.isActive=!0,s.startEvent=s.currentEvent=t,s.container=l,s.containingBlock=d,s.elementClientX=_.left,s.elementClientY=_.top,s.left=s.gridX=u,s.top=s.gridY=c,n._emit("dragInit",e,t),l!==h&&(s.containerDiffX=f.left,s.containerDiffY=f.top,i.parentNode===l?(s.gridX=u-s.containerDiffX,s.gridY=c-s.containerDiffY):(s.left=u+s.containerDiffX,s.top=c+s.containerDiffY,l.appendChild(i),C(i,{transform:S(s.left,s.top)}))),b(i,r.itemDraggingClass),this.bindScrollListeners(),n._emit("dragStart",e,t),this},l.prototype.onMove=function(t){var e=this,i=e.getItem();if(!i._isActive)return e.stop();var n=i._element,r=e.getGrid(),s=r._settings,o=e._data,a=s.dragAxis,h=t.deltaX-o.currentEvent.deltaX,l=t.deltaY-o.currentEvent.deltaY;return it.add(st,i._id,function(){o.currentEvent=t,"y"!==a&&(o.left+=h,o.gridX+=h,o.elementClientX+=h),"x"!==a&&(o.top+=l,o.gridY+=l,o.elementClientY+=l),s.dragSort&&e._checkSortOverlap()},function(){C(n,{transform:S(o.left,o.top)}),r._emit("dragMove",i,t)}),e},l.prototype.onScroll=function(t){var e,i,n,r,s=this,o=s.getItem(),a=o._element,h=s.getGrid(),l=h._settings,d=l.dragAxis,f=s._data,u=h._element;return it.add(ot,o._id,function(){e=a.getBoundingClientRect(),i=f.elementClientX-e.left,n=f.elementClientY-e.top,f.container!==u&&(r=H(f.containingBlock,u),f.containerDiffX=r.left,f.containerDiffY=r.top),"y"!==d&&(f.left+=i,f.gridX=f.left-f.containerDiffX),"x"!==d&&(f.top+=n,f.gridY=f.top-f.containerDiffY),l.dragSort&&s._checkSortOverlap()},function(){C(a,{transform:S(f.left,f.top)}),h._emit("dragScroll",o,t)}),s},l.prototype.onEnd=function(t){var e=this.getItem(),i=e._element,n=this.getGrid(),r=n._settings,s=this._data,o=e._release;return e._isActive?(this.cancelRafLoop(),r.dragSort&&this._checkSortOverlap("finish"),this.unbindScrollListeners(),o.containerDiffX=s.containerDiffX,o.containerDiffY=s.containerDiffY,this.reset(),A(i,r.itemDraggingClass),n._emit("dragEnd",e,t),this._isMigrating?this.finishMigration():o.start(),this):this.stop()},B.getSlot=function(t,e,i,n,r,s){var o,a,h,l,d,f=[],u={left:null,top:null,width:i,height:n};for(l=0;l<e.length;l++)if(o=e[l],u.width<=o.width+.001&&u.height<=o.height+.001){u.left=o.left,u.top=o.top;break}for(null===u.left&&(u.left=r?0:t.width,u.top=r?t.height:0,s||(h=!0)),r&&u.top+u.height>t.height&&(u.left>0&&f.push({left:0,top:t.height,width:u.left,height:1/0}),u.left+u.width<t.width&&f.push({left:u.left+u.width,top:t.height,width:t.width-u.left-u.width,height:1/0}),t.height=u.top+u.height),!r&&u.left+u.width>t.width&&(u.top>0&&f.push({left:t.width,top:0,width:1/0,height:u.top}),u.top+u.height<t.height&&f.push({left:t.width,top:u.top+u.height,width:1/0,height:t.height-u.top-u.height}),t.width=u.left+u.width),l=s?0:h?e.length:l;l<e.length;l++)for(a=B.splitRect(e[l],u),d=0;d<a.length;d++)(o=a[d]).width>.49&&o.height>.49&&(r&&o.top<t.height||!r&&o.left<t.width)&&f.push(o);return f.length&&(f=B.purgeRects(f).sort(r?B.sortRectsTopLeft:B.sortRectsLeftTop)),[u,f]},B.splitRect=function(t,e){var i=[];return B.doRectsOverlap(t,e)?(t.left<e.left&&i.push({left:t.left,top:t.top,width:e.left-t.left,height:t.height}),t.left+t.width>e.left+e.width&&i.push({left:e.left+e.width,top:t.top,width:t.left+t.width-(e.left+e.width),height:t.height}),t.top<e.top&&i.push({left:t.left,top:t.top,width:t.width,height:e.top-t.top}),t.top+t.height>e.top+e.height&&i.push({left:t.left,top:e.top+e.height,width:t.width,height:t.top+t.height-(e.top+e.height)}),i):[{left:t.left,top:t.top,width:t.width,height:t.height}]},B.doRectsOverlap=function(t,e){return!(t.left+t.width<=e.left||e.left+e.width<=t.left||t.top+t.height<=e.top||e.top+e.height<=t.top)},B.isRectWithinRect=function(t,e){return t.left>=e.left&&t.top>=e.top&&t.left+t.width<=e.left+e.width&&t.top+t.height<=e.top+e.height},B.purgeRects=function(t){for(var e,i,n,r=t.length;r--;)for(i=t[r],e=t.length;e--;)if(n=t[e],r!==e&&B.isRectWithinRect(i,n)){t.splice(r,1);break}return t},B.sortRectsTopLeft=function(t,e){return t.top-e.top||t.left-e.left},B.sortRectsLeftTop=function(t,e){return t.left-e.left||t.top-e.top},n});