<!DOCTYPE html>
<html lang="vi">
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="description" content="Hệ T<PERSON>ống Quản Lý Hiện Đại Vớ<PERSON> Gia<PERSON> Diện T<PERSON>ân Thiện">
    <meta name="keywords" content="admin template, ra-admin admin template, dashboard template, flat admin template, responsive admin template, web app">
    <meta name="author" content="<PERSON><PERSON> Thống Quản Lý">
    <meta name="csrf-token" content="{{ csrf_token() }}">
    <link rel="icon" href="{{ asset('assets/images/logo/favicon.png') }}" type="image/x-icon">
    <link rel="shortcut icon" href="{{ asset('assets/images/logo/favicon.png') }}" type="image/x-icon">
    <title>@yield('title', '<PERSON><PERSON>ng <PERSON> | <PERSON><PERSON> T<PERSON>ố<PERSON> L<PERSON>')</title>

    <!-- Animation css -->
    <link rel="stylesheet" href="{{ asset('assets/vendor/animation/animate.min.css') }}">

    <!-- Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Golos+Text:wght@400..900&display=swap" rel="stylesheet">

    <!-- wheather icon css-->
    <link rel="stylesheet" type="text/css" href="{{ asset('assets/vendor/weather/weather-icons.css') }}">
    <link rel="stylesheet" type="text/css" href="{{ asset('assets/vendor/weather/weather-icons-wind.css') }}">

    <!--flag Icon css-->
    <link rel="stylesheet" type="text/css" href="{{ asset('assets/vendor/flag-icons-master/flag-icon.css') }}">

    <!-- tabler icons-->
    <link rel="stylesheet" type="text/css" href="{{ asset('assets/vendor/tabler-icons/tabler-icons.css') }}">

    <!-- prism css-->
    <link rel="stylesheet" type="text/css" href="{{ asset('assets/vendor/prism/prism.min.css') }}">

    <!-- apexcharts css-->
    <link rel="stylesheet" type="text/css" href="{{ asset('assets/vendor/apexcharts/apexcharts.css') }}">

    <!-- glight css -->
    <link rel="stylesheet" href="{{ asset('assets/vendor/glightbox/glightbox.min.css') }}">

    <!-- slick css -->
    <link rel="stylesheet" href="{{ asset('assets/vendor/slick/slick.css') }}">
    <link rel="stylesheet" href="{{ asset('assets/vendor/slick/slick-theme.css') }}">

    <!-- Data Table css-->
    <link rel="stylesheet" type="text/css" href="{{ asset('assets/vendor/datatable/jquery.dataTables.min.css') }}">

    <!-- Bootstrap css-->
    <link rel="stylesheet" type="text/css" href="{{ asset('assets/vendor/bootstrap/bootstrap.min.css') }}">

    <!-- vector map css -->
    <link rel="stylesheet" href="{{ asset('assets/vendor/vector-map/jquery-jvectormap.css') }}">

    <!-- simplebar css-->
    <link rel="stylesheet" type="text/css" href="{{ asset('assets/vendor/simplebar/simplebar.css') }}">

    <!-- App css-->
    <link rel="stylesheet" type="text/css" href="{{ asset('assets/css/style.css') }}">

    <!-- Responsive css-->
    <link rel="stylesheet" type="text/css" href="{{ asset('assets/css/responsive.css') }}">

    <style>
        /* Custom Styles For Vietnamese Text */
        h1, h2, h3, h4, h5, h6 {
            text-transform: capitalize;
        }

        .card-title {
            text-transform: capitalize;
        }

        .btn {
            text-transform: capitalize;
        }

        label {
            text-transform: capitalize;
        }

        .table th {
            text-transform: capitalize;
        }

        .breadcrumb-item {
            text-transform: capitalize;
        }

        .dropdown-item {
            text-transform: capitalize;
        }

        .alert {
            text-transform: capitalize;
        }

        .nav-link {
            text-transform: capitalize;
        }

        .menu-title span {
            text-transform: capitalize;
        }
    </style>

    @stack('styles')
</head>
<body>
    <div class="app-wrapper">
        <div class="loader-wrapper">
            <div class="loader_16"></div>
        </div>

        <!-- Menu Navigation starts -->
        <nav>
            <div class="app-logo">
                <a class="logo d-inline-block" href="{{ route('admin.dashboard') }}">
                    <i class="fa fa-code"></i>
                </a>

                <span class="bg-light-primary toggle-semi-nav">
                    <i class="ti ti-chevrons-right f-s-20"></i>
                </span>
            </div>
            <div class="app-nav" id="app-simple-bar">
                <ul class="main-nav p-0 mt-2">
                    <li class="menu-title">
                        <span>Bảng Điều Khiển</span>
                    </li>
                    <li>
                        <a class="{{ request()->routeIs('admin.dashboard') ? 'active' : '' }}" href="{{ route('admin.dashboard') }}">
                            <i class="ph-duotone ph-house-line"></i>
                            Trang Chủ
                        </a>
                    </li>
                    <li>
                        <a class="{{ request()->routeIs('admin.users*') ? 'active' : '' }}" href="{{ route('admin.users') }}">
                            <i class="ph-duotone ph-users"></i>
                            Quản Lý Người Dùng
                        </a>
                    </li>

                    <li class="menu-title">
                        <span>Ứng Dụng</span>
                    </li>
                    <li>
                        <a class="" data-bs-toggle="collapse" href="#apps" aria-expanded="false">
                            <i class="ph-duotone ph-stack"></i>
                            Ứng Dụng
                        </a>
                        <ul class="collapse" id="apps">
                            <li><a href="#">Lịch</a></li>
                            <li><a href="#">Hồ Sơ</a></li>
                            <li><a href="#">Dự Án</a></li>
                            <li><a href="#">Công Việc</a></li>
                            <li><a href="#">Nhóm</a></li>
                            <li><a href="#">API</a></li>
                            <li><a href="#">Ticket</a></li>
                            <li><a href="#">Email</a></li>
                            <li><a href="#">Hóa Đơn</a></li>
                            <li><a href="#">Chat</a></li>
                            <li><a href="#">Quản Lý File</a></li>
                            <li><a href="#">Bookmark</a></li>
                            <li><a href="#">Kanban Board</a></li>
                            <li><a href="#">Timeline</a></li>
                            <li><a href="#">FAQ</a></li>
                            <li><a href="#">Bảng Giá</a></li>
                            <li><a href="#">Thư Viện</a></li>
                        </ul>
                    </li>

                    <li class="no-sub">
                        <a class="" href="#">
                            <i class="ph-duotone ph-squares-four"></i> Widget
                        </a>
                    </li>

                    <li class="menu-title">
                        <span>Thành Phần</span>
                    </li>
                    <li>
                        <a class="" data-bs-toggle="collapse" href="#ui-kits" aria-expanded="false">
                            <i class="ph-duotone ph-briefcase"></i>
                            UI Kits
                        </a>
                        <ul class="collapse" id="ui-kits">
                            <li><a href="#">Cheatsheet</a></li>
                            <li><a href="#">Alert</a></li>
                            <li><a href="#">Badges</a></li>
                            <li><a href="#">Buttons</a></li>
                            <li><a href="#">Cards</a></li>
                            <li><a href="#">Dropdown</a></li>
                            <li><a href="#">Grid</a></li>
                            <li><a href="#">Avatar</a></li>
                            <li><a href="#">Tabs</a></li>
                            <li><a href="#">Accordions</a></li>
                            <li><a href="#">Progress</a></li>
                            <li><a href="#">Notifications</a></li>
                            <li><a href="#">Lists</a></li>
                            <li><a href="#">Helper Classes</a></li>
                            <li><a href="#">Background</a></li>
                            <li><a href="#">Divider</a></li>
                            <li><a href="#">Ribbons</a></li>
                            <li><a href="#">Editor</a></li>
                            <li><a href="#">Collapse</a></li>
                            <li><a href="#">Footer</a></li>
                            <li><a href="#">Shadow</a></li>
                            <li><a href="#">Wrapper</a></li>
                            <li><a href="#">Bullet</a></li>
                            <li><a href="#">Placeholder</a></li>
                            <li><a href="#">Alignment</a></li>
                        </ul>
                    </li>
                </ul>
            </div>

            <div class="menu-navs">
                <span class="menu-previous"><i class="ti ti-chevron-left"></i></span>
                <span class="menu-next"><i class="ti ti-chevron-right"></i></span>
            </div>
        </nav>
        <!-- Menu Navigation ends -->

        <div class="app-content">
            <div class="">
                <!-- Header Section starts -->
                <header class="header-main">
                    <div class="container-fluid">
                        <div class="row">
                            <div class="col-6 col-sm-4 d-flex align-items-center header-left p-0">
                                <span class="header-toggle me-3">
                                    <i class="ph ph-circles-four"></i>
                                </span>
                                @yield('breadcrumb')
                            </div>

                            <div class="col-6 col-sm-8 d-flex align-items-center justify-content-end header-right p-0">
                                <ul class="d-flex align-items-center">
                                    <li class="header-notification">
                                        <a href="#" class="head-icon" role="button" data-bs-toggle="dropdown" aria-expanded="false">
                                            <i class="ph-duotone ph-bell text-primary f-s-26"></i>
                                            <span class="badge bg-danger rounded-pill">3</span>
                                        </a>
                                        <ul class="dropdown-menu notification-dropdown header-card border-0">
                                            <li class="dropdown-item p-3">
                                                <h6 class="mb-0">Thông Báo</h6>
                                            </li>
                                            <li><hr class="dropdown-divider"></li>
                                            <li class="dropdown-item">
                                                <div class="d-flex align-items-center">
                                                    <div class="flex-shrink-0">
                                                        <i class="ph-duotone ph-user-plus text-success f-s-20"></i>
                                                    </div>
                                                    <div class="flex-grow-1 ms-3">
                                                        <h6 class="mb-1">Người Dùng Mới</h6>
                                                        <p class="mb-0 text-muted">Có Người Dùng Mới Đăng Ký</p>
                                                    </div>
                                                </div>
                                            </li>
                                        </ul>
                                    </li>

                                    <li class="header-profile">
                                        <div class="dropdown">
                                            <a href="#" class="d-flex align-items-center head-icon" data-bs-toggle="dropdown" aria-expanded="false">
                                                <div class="avatar-sm bg-primary text-white rounded-circle d-flex align-items-center justify-content-center me-2">
                                                    {{ strtoupper(substr(Auth::user()->name, 0, 1)) }}
                                                </div>
                                                <span class="d-none d-md-inline">{{ Auth::user()->name }}</span>
                                                <i class="ph ph-caret-down ms-2"></i>
                                            </a>
                                            <ul class="dropdown-menu profile-dropdown header-card border-0">
                                                <li class="dropdown-item p-3">
                                                    <div class="d-flex align-items-center">
                                                        <div class="avatar-sm bg-primary text-white rounded-circle d-flex align-items-center justify-content-center me-3">
                                                            {{ strtoupper(substr(Auth::user()->name, 0, 1)) }}
                                                        </div>
                                                        <div>
                                                            <h6 class="mb-0">{{ Auth::user()->name }}</h6>
                                                            <p class="mb-0 text-muted">{{ Auth::user()->email }}</p>
                                                        </div>
                                                    </div>
                                                </li>
                                                <li><hr class="dropdown-divider"></li>
                                                <li><a class="dropdown-item" href="{{ route('profile.edit') }}">
                                                    <i class="ph-duotone ph-user me-2"></i>
                                                    Hồ Sơ Cá Nhân
                                                </a></li>
                                                <li><a class="dropdown-item" href="#">
                                                    <i class="ph-duotone ph-gear me-2"></i>
                                                    Cài Đặt
                                                </a></li>
                                                <li><a class="dropdown-item" href="{{ route('user.dashboard') }}">
                                                    <i class="ph-duotone ph-house me-2"></i>
                                                    Xem Trang User
                                                </a></li>
                                                <li><hr class="dropdown-divider"></li>
                                                <li>
                                                    <form method="POST" action="{{ route('logout') }}">
                                                        @csrf
                                                        <button type="submit" class="dropdown-item">
                                                            <i class="ph-duotone ph-sign-out me-2"></i>
                                                            Đăng Xuất
                                                        </button>
                                                    </form>
                                                </li>
                                            </ul>
                                        </div>
                                    </li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </header>
                <!-- Header Section ends -->

                <!-- Main Content Section starts -->
                <div class="main-content">
                    <div class="container-fluid">
                        @if(session('success'))
                            <div class="alert alert-success alert-dismissible fade show" role="alert">
                                <i class="ph-duotone ph-check-circle me-2"></i>
                                {{ session('success') }}
                                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                            </div>
                        @endif

                        @if(session('error'))
                            <div class="alert alert-danger alert-dismissible fade show" role="alert">
                                <i class="ph-duotone ph-x-circle me-2"></i>
                                {{ session('error') }}
                                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                            </div>
                        @endif

                        @yield('content')
                    </div>
                </div>
                <!-- Main Content Section ends -->
            </div>
        </div>
    </div>

    <!-- latest jquery-->
    <script src="{{ asset('assets/js/jquery-3.6.3.min.js') }}"></script>

    <!-- Bootstrap js-->
    <script src="{{ asset('assets/vendor/bootstrap/bootstrap.bundle.min.js') }}"></script>

    <!-- simplebar js-->
    <script src="{{ asset('assets/vendor/simplebar/simplebar.min.js') }}"></script>

    <!-- custom js -->
    <script src="{{ asset('assets/js/script.js') }}"></script>

    <script>
        // Remove loader after page load
        $(window).on('load', function() {
            $('.loader-wrapper').fadeOut('slow');
        });

        // Initialize sidebar
        $(document).ready(function() {
            // Toggle sidebar
            $('.toggle-semi-nav').on('click', function() {
                $('nav').toggleClass('semi-nav');
            });

            // Header toggle
            $('.header-toggle').on('click', function() {
                $('nav').toggleClass('close-nav');
            });
        });
    </script>

    @stack('scripts')
</body>
</html>
