<?php $__env->startSection('title', '<PERSON>ảng Đ<PERSON> D<PERSON>ng'); ?>

<?php $__env->startSection('content'); ?>
<!-- Welcome Card -->
<div class="card bg-primary-subtle mb-4">
    <div class="card-body">
        <div class="row align-items-center">
            <div class="col-md-8">
                <h3 class="card-title text-primary mb-2">Ch<PERSON>o Mừng, <?php echo e(Auth::user()->name); ?>!</h3>
                <p class="card-text mb-3">Chào Mừng Bạn Đến Với Hệ Thống Quản Lý Cá Nhân</p>
                <a href="<?php echo e(route('user.profile')); ?>" class="btn btn-primary">
                    Xem Hồ Sơ Cá Nhân
                </a>
            </div>
            <div class="col-md-4 text-center">
                <div class="avatar-xl bg-primary text-white rounded-circle d-inline-flex align-items-center justify-content-center">
                    <?php echo e(strtoupper(substr(Auth::user()->name, 0, 2))); ?>

                </div>
            </div>
        </div>
    </div>
</div>

<!-- Quick Stats -->
<div class="row mb-4">
    <div class="col-md-3 mb-3">
        <div class="card text-center">
            <div class="card-body">
                <div class="mb-3">
                    <iconify-icon icon="solar:user-check-line-duotone" class="fs-1 text-success"></iconify-icon>
                </div>
                <h5 class="card-title">Tài Khoản Hoạt Động</h5>
                <p class="text-muted">Trạng Thái Bình Thường</p>
            </div>
        </div>
    </div>

    <div class="col-md-3 mb-3">
        <div class="card text-center">
            <div class="card-body">
                <div class="mb-3">
                    <iconify-icon icon="solar:shield-check-line-duotone" class="fs-1 text-primary"></iconify-icon>
                </div>
                <h5 class="card-title">Bảo Mật Cao</h5>
                <p class="text-muted">Thông Tin An Toàn</p>
            </div>
        </div>
    </div>

    <div class="col-md-3 mb-3">
        <div class="card text-center">
            <div class="card-body">
                <div class="mb-3">
                    <iconify-icon icon="solar:headphones-round-sound-line-duotone" class="fs-1 text-warning"></iconify-icon>
                </div>
                <h5 class="card-title">Hỗ Trợ 24/7</h5>
                <p class="text-muted">Luôn Sẵn Sàng</p>
            </div>
        </div>
    </div>

    <div class="col-md-3 mb-3">
        <div class="card text-center">
            <div class="card-body">
                <div class="mb-3">
                    <iconify-icon icon="solar:refresh-line-duotone" class="fs-1 text-info"></iconify-icon>
                </div>
                <h5 class="card-title">Cập Nhật Thường Xuyên</h5>
                <p class="text-muted">Luôn Mới Nhất</p>
            </div>
        </div>
    </div>
</div>

<!-- Main Content -->
<div class="row">
    <div class="col-md-8 mb-4">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">Thông Tin Tài Khoản</h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6 mb-3">
                        <small class="text-muted">Tên Đầy Đủ</small>
                        <div class="fw-semibold"><?php echo e(Auth::user()->name); ?></div>
                    </div>

                    <div class="col-md-6 mb-3">
                        <small class="text-muted">Địa Chỉ Email</small>
                        <div class="fw-semibold"><?php echo e(Auth::user()->email); ?></div>
                    </div>

                    <div class="col-md-6 mb-3">
                        <small class="text-muted">Vai Trò</small>
                        <div>
                            <span class="badge <?php echo e(Auth::user()->role === 'admin' ? 'bg-danger' : 'bg-primary'); ?>">
                                <?php echo e(Auth::user()->role === 'admin' ? 'Quản Trị Viên' : 'Người Dùng'); ?>

                            </span>
                        </div>
                    </div>

                    <div class="col-md-6 mb-3">
                        <small class="text-muted">Ngày Tham Gia</small>
                        <div class="fw-semibold"><?php echo e(Auth::user()->created_at->format('d/m/Y')); ?></div>
                    </div>
                </div>

                <div class="d-flex gap-2 mt-3">
                    <a href="<?php echo e(route('user.profile')); ?>" class="btn btn-primary btn-sm">
                        Xem Hồ Sơ Chi Tiết
                    </a>
                    <a href="<?php echo e(route('profile.edit')); ?>" class="btn btn-outline-primary btn-sm">
                        Chỉnh Sửa Thông Tin
                    </a>
                </div>
            </div>
        </div>
    </div>

    <div class="col-md-4 mb-4">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">Thao Tác Nhanh</h5>
            </div>
            <div class="card-body">
                <div class="d-grid gap-2">
                    <a href="<?php echo e(route('user.profile')); ?>" class="btn btn-primary btn-sm">
                        <iconify-icon icon="solar:user-line-duotone" class="me-2"></iconify-icon>
                        Xem Hồ Sơ Cá Nhân
                    </a>
                    <a href="<?php echo e(route('profile.edit')); ?>" class="btn btn-outline-primary btn-sm">
                        <iconify-icon icon="solar:settings-line-duotone" class="me-2"></iconify-icon>
                        Chỉnh Sửa Thông Tin
                    </a>
                    <?php if(Auth::user()->isAdmin()): ?>
                        <a href="<?php echo e(route('admin.dashboard')); ?>" class="btn btn-outline-danger btn-sm">
                            <iconify-icon icon="solar:shield-user-line-duotone" class="me-2"></iconify-icon>
                            Truy Cập Quản Trị
                        </a>
                    <?php endif; ?>
                    <button class="btn btn-outline-secondary btn-sm">
                        <iconify-icon icon="solar:lock-password-line-duotone" class="me-2"></iconify-icon>
                        Thay Đổi Mật Khẩu
                    </button>
                </div>
            </div>
        </div>

        <div class="card mt-3">
            <div class="card-header">
                <h5 class="card-title mb-0">Thống Kê</h5>
            </div>
            <div class="card-body">
                <div class="row text-center">
                    <div class="col-6">
                        <h4 class="text-primary mb-1"><?php echo e(Auth::user()->created_at->diffInDays(now())); ?></h4>
                        <small class="text-muted">Ngày Tham Gia</small>
                    </div>
                    <div class="col-6">
                        <h4 class="text-success mb-1">
                            <iconify-icon icon="solar:check-circle-line-duotone"></iconify-icon>
                        </h4>
                        <small class="text-muted">Hoạt Động</small>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<?php $__env->stopSection(); ?>

<?php $__env->startPush('styles'); ?>
<style>
    .avatar-xl {
        width: 80px;
        height: 80px;
        font-size: 24px;
        font-weight: 600;
    }
</style>
<?php $__env->stopPush(); ?>

<?php echo $__env->make('layouts.user', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH D:\Src Công Việc\laravel-project\resources\views/user/dashboard.blade.php ENDPATH**/ ?>